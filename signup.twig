<!-- Custom Signup Page Design -->
<style>
.signup-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #e8f4fd 0%, #fce4ec 25%, #f3e5f5 50%, #e1f5fe 75%, #fff3e0 100%);
    display: flex;
    align-items: center;
    padding: 0;
    position: relative;
    overflow: hidden;
}

.signup-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background:
        radial-gradient(circle at 20% 30%, rgba(33, 150, 243, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(156, 39, 176, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(255, 193, 7, 0.1) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    33% { transform: translate(-20px, -20px) rotate(1deg); }
    66% { transform: translate(20px, -10px) rotate(-1deg); }
}

.signup-content {
    max-width: 1400px;
    width: 100%;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
    padding: 60px 40px;
    position: relative;
    z-index: 1;
}

.signup-illustration {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    padding: 40px;
}

.signup-illustration::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 40%, rgba(33, 150, 243, 0.15) 0%, transparent 60%),
        radial-gradient(circle at 70% 60%, rgba(156, 39, 176, 0.15) 0%, transparent 60%),
        radial-gradient(circle at 50% 20%, rgba(255, 193, 7, 0.1) 0%, transparent 50%),
        linear-gradient(45deg, rgba(33, 150, 243, 0.05) 0%, rgba(156, 39, 176, 0.05) 100%);
    border-radius: 20px;
    z-index: -1;
}

.signup-illustration img {
    max-width: 100%;
    height: auto;
    max-height: 500px;
    width: auto;
    position: relative;
    z-index: 1;
}

.signup-form-section {
    padding: 60px 40px;
    max-width: 500px;
}

.signup-title {
    font-size: 42px;
    font-weight: 700;
    color: #333;
    margin-bottom: 12px;
    line-height: 1.2;
}

.signup-title .account-text {
    color: #2196f3;
}

.signup-subtitle {
    color: #666;
    font-size: 16px;
    margin-bottom: 40px;
    line-height: 1.6;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    color: #666;
    font-size: 14px;
    margin-bottom: 10px;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 16px 20px;
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    font-size: 16px;
    background: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #2196f3;
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 20px 0;
}

.checkbox-group input[type="checkbox"] {
    width: 18px;
    height: 18px;
}

.checkbox-group label {
    font-size: 13px;
    color: #666;
    margin: 0;
}

.checkbox-group a {
    color: #2196f3;
    text-decoration: none;
}

.signup-btn {
    width: 100%;
    background: #2196f3;
    color: white;
    border: none;
    padding: 18px;
    border-radius: 10px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 25px;
}

.signup-btn:hover {
    background: #1976d2;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.google-signin-wrapper {
    width: 100%;
    margin-bottom: 25px;
}

.google-signin-custom {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 16px;
    border: 1px solid #dadce0;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.9);
    color: #3c4043;
    text-decoration: none;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
    font-family: 'Roboto', sans-serif;
}

.google-signin-custom:hover {
    background: rgba(255, 255, 255, 1);
    border-color: #dadce0;
    color: #3c4043;
    text-decoration: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.google-icon {
    width: 20px;
    height: 20px;
    background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAxOCAxOCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgPHBhdGggZD0iTTE3LjY0IDkuMjA0NTQ1NDVjMC0uNjM5NzI3MjctLjA1NzI3MjczLTEuMjUyMjcyNzMtLjE2MzYzNjM2LTEuODQwOTA5MDlIOXY0LjQ2MzYzNjM2aDQuODQ0MDkwOTFjLS4yMDQ1NDU0NSAxLjEyNS0uODI5NTQ1NDUgMi4wNzk1NDU0NS0xLjc1IDIuNzE1OTA5MDl2Mi4yNTgxODE4MmgzLjE5MDkwOTFjMS42NTY4MTgxOC0xLjUyNzI3MjczIDIuNjA5MDkwOTEtMy43NzI3MjcyNyAyLjYwOTA5MDkxLTYuNTk3NzI3Mjd6IiBmaWxsPSIjNDI4NUY0Ii8+CiAgICA8cGF0aCBkPSJNOSAxOGMxLjY2MzYzNjM2IDAgMy4wNTQ1NDU0NS0uNTU0NTQ1NDUgNC4wNzI3MjcyNy0xLjUwNjgxODE4bC0zLjE5MDkwOTEtMi4yNTgxODE4MmMtLjU1NDU0NTQ1LjM3NSAxLjI2ODE4MTgyLjU5NzcyNzI3IDIuMTE4MTgxODIuNTk3NzI3MjcgMS42MzYzNjM2NCAwIDMuMDE4MTgxODItMS4xMDIyNzI3MyAzLjUxMzYzNjM2LTIuNTc5NTQ1NDVIMTAuNVY5LjI0NTQ1NDU1SDUuMjI3MjcyNzNjLS4zNzUgMS4xNzk1NDU0NS0uNTk3NzI3MjcgMi40MjI3MjcyNy0uNTk3NzI3MjcgMy43NTQ1NDU0NSAwIDEuMzMxODE4MTguMjIyNzI3MjcgMi41NzUuNTk3NzI3MjcgMy43NTQ1NDU0NWgzLjI3MjcyNzI3eiIgZmlsbD0iIzM0QTg1MyIvPgogICAgPHBhdGggZD0iTTUuMjI3MjcyNzMgMTAuNzVjMC0uNjk3NzI3MjcuMTIyNzI3MjctMS4zNjM2MzYzNi4zNDA5MDkwOS0xLjk3NzI3MjczTDIuMjk1NDU0NTUgNi4yMTgxODE4MkMxLjA0NzcyNzI3IDcuNjkzMTgxODIuNDMxODE4MTggOS4yOTU0NTQ1NS40MzE4MTgxOCAxMWMwIDEuNzA0NTQ1NDUuNjE1OTA5MDkgMy4zMDY4MTgxOCAxLjg2MzYzNjM3IDQuNzgxODE4MThsMy4yNzI3MjcyOC0yLjU1NDU0NTQ1Yy0uMjE4MTgxODItLjYxMzYzNjM2LS4zNDA5MDkwOS0xLjI3OTU0NTQ1LS4zNDA5MDkwOS0xLjk3NzI3MjczeiIgZmlsbD0iI0ZCQkMwNSIvPgogICAgPHBhdGggZD0iTTkgMy42ODE4MTgxOGMxLjMyMTgxODE4IDAgMi41MDkwOTA5MS40NTQ1NDU0NSAzLjQzNjM2MzY0IDEuMzQwOTA5MDlsMi41OTA5MDkwOS0yLjU5MDkwOTA5QzEzLjU0NTQ1NDU1LjgxODE4MTgyIDExLjU0NTQ1NDU1IDAgOSAwIDUuNDgxODE4MTggMCAyLjU2ODE4MTgyIDIuMzg2MzYzNjQgMS4yMjcyNzI3MyA1LjY4MTgxODE4bDMuMjcyNzI3MjcgMi41NTQ1NDU0NWMuNzYzNjM2MzYtMi4yNSAyLjg0MDkwOTA5LTMuODg2MzYzNjQgNS41LTMuODg2MzYzNjR6IiBmaWxsPSIjRUE0MzM1Ii8+CiAgPC9nPgo8L3N2Zz4K') no-repeat center;
    background-size: contain;
}

/* Style the actual Google button */
.g_id_signin {
    width: 100% !important;
    margin-bottom: 25px !important;
}

.g_id_signin > div {
    width: 100% !important;
    border-radius: 10px !important;
    padding: 16px !important;
    font-size: 16px !important;
}

.signin-link {
    text-align: center;
    color: #666;
    font-size: 16px;
}

.signin-link a {
    color: #2196f3;
    text-decoration: none;
    font-weight: 600;
}

.alert {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 14px;
}

.alert-danger {
    background: #ffebee;
    color: #c62828;
    border: 1px solid #ffcdd2;
}

.alert-success {
    background: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
}

@media (max-width: 768px) {
    .signup-content {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .signup-illustration {
        order: 2;
    }

    .signup-form-section {
        padding: 30px 10px;
        order: 1;
    }

    .signup-title {
        font-size: 28px;
    }
}
</style>

<div class="signup-container">
    <div class="signup-content">
        <!-- Left Side - Illustration -->
        <div class="signup-illustration">
            <img src="https://storage.perfectcdn.com/n675sa/bo73l3570qotce23.png" alt="Signup Illustration">
        </div>

        <!-- Right Side - Form -->
        <div class="signup-form-section">
            <h1 class="signup-title">Create an <span class="account-text">Account</span></h1>
            <p class="signup-subtitle">Would you like to get started? Sign up quickly and enjoy a satisfying experience with our services.</p>

            <form {% if site['rtl'] %}class="rtl-form"{% endif %} action="" method="post">
                {% if error %}
                    <div class="alert alert-danger">
                        {{ errorMessage }}
                    </div>
                {% endif %}
                {% if success %}
                    <div class="alert alert-success">
                        {{ successText }}
                    </div>
                {% endif %}

                {% for field in fields %}
                    <div class="form-group">
                        <label for="{{ field.code }}">{{ lang(field.label) }}</label>
                        <input type="{{ field.type }}" class="form-control" id="{{ field.code }}" value="{{ field.value }}" name="RegistrationForm[{{ field.code }}]" placeholder="{{ lang(field.label) }}">
                    </div>
                {% endfor %}

                {% if site['captcha'] %}
                    <div class="form-group">
                        {{ captchaCode }}
                    </div>
                {% endif %}

                {% if check_agreement %}
                    <div class="checkbox-group">
                        <input type="checkbox" class="terms-accept-checkbox" name="RegistrationForm[termsofservice]" id="terms">
                        <label for="terms">{{ lang('signup.accept_terms_text') }} <a href="{{ page_url('terms') }}" target="_blank">{{ lang('terms.signup_checkbox') }}</a></label>
                    </div>
                {% endif %}

                <input type="hidden" name="_csrf" value="{{csrftoken}}">
                <button type="submit" class="signup-btn">{{ lang('signup.button') }}</button>

                {% if googleSignIn %}
                    <div id="g_id_onload"
                       data-client_id="{{ googleClientId }}"
                       data-login_uri="{{ googleSignInRedirectUrl }}"
                       data-auto_prompt="false"
                       data-_csrf="{{csrftoken}}">
                    </div>
                    <div class="google-signin-wrapper">
                        <div class="g_id_signin"
                           data-type="standard"
                           data-size="large"
                           data-theme="outline"
                           data-text="signup_with"
                           data-shape="rectangular"
                           data-logo_alignment="left"
                           data-width="100%">
                        </div>
                    </div>
                {% endif %}

                <div class="signin-link">
                    Already have an account? <a href="{{ page_url('signin') }}">Sign in</a>
                </div>
            </form>
        </div>
    </div>
</div>
