<!-- Exact Match Design -->
<div class="neworder-page">
  <!-- Top Blue Header Bar -->
  <div class="top-header-bar">
    <div class="header-content">
      <span class="current-page">You are currently on: <strong>New order</strong></span>
      <span class="panel-orders">Panel Orders: <strong>{{ totals['ordersAll'] }}</strong></span>
      <span class="my-orders">My Orders: <strong>{{ user['ordersActive'] }}</strong></span>
      <span class="total-spend">Total Spend: <strong>{{ user['spent_formatted'] }}</strong></span>
      <span class="welcome-balance">Welcome {{ user['username'] }}, Your Balance is: <strong>{{ user['balance_formatted'] }}</strong></span>
    </div>
  </div>

  <!-- Platform Buttons Row -->
  <div class="platform-buttons-row">
    <div class="platform-container">
      <div class="platform-btn" data-platform="everything">
        <i class="fas fa-globe"></i>
        <span>Everything</span>
      </div>
      <div class="platform-btn" data-platform="youtube">
        <i class="fab fa-youtube"></i>
        <span>YouTube</span>
      </div>
      <div class="platform-btn" data-platform="tiktok">
        <i class="fab fa-tiktok"></i>
        <span>TikTok</span>
      </div>
      <div class="platform-btn" data-platform="telegram">
        <i class="fab fa-telegram"></i>
        <span>Telegram</span>
      </div>
      <div class="platform-btn" data-platform="facebook">
        <i class="fab fa-facebook"></i>
        <span>Facebook</span>
      </div>
      <div class="platform-btn" data-platform="instagram">
        <i class="fab fa-instagram"></i>
        <span>Instagram</span>
      </div>
      <div class="platform-btn" data-platform="facebook-live">
        <i class="fab fa-facebook"></i>
        <span>Facebook Live</span>
      </div>
      <div class="platform-btn" data-platform="website-traffic">
        <i class="fas fa-chart-line"></i>
        <span>Website Traffic</span>
      </div>
    </div>
  </div>

  <!-- Main Content Layout -->
  <div class="main-layout">
    <div class="content-wrapper">
      <!-- Left Side - Form -->
      <div class="left-column">

        <!-- Success/Error Messages -->
        {% if error %}
          <div class="alert alert-dismissible alert-danger">
            <button type="button" class="close" data-dismiss="alert">&times;</button>
            {{ errorMessage }}
          </div>
        {% endif %}

        {% if 1 == success %}
          <div class="alert alert-success alert-dismissible" role="alert">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4>{{lang('neworder.message.success')}}</h4>
            {{lang('neworder.id')}}: {{order['id']}}<br>
            {{lang('neworder.service')}}: {{order['service']}}<br>
            {{lang('neworder.link')}}: {{order['link']}}<br>
            {{lang('neworder.quantity')}}: {{order['quantity']}}<br>
            {{ lang('neworder.charge') }}: {% if order.converted %} <span data-toggle="tooltip" data-placement="top" title="{{ order.original_charge }}">{{ order['charge'] }}</span> {% else %} {{ order['charge'] }} {% endif %}<br>
            {{ lang('neworder.balance') }}: {% if order.converted %} <span data-toggle="tooltip" data-placement="top" title="{{ order.original_balance }}">{{ order['balance'] }}</span> {% else %} {{ order['balance'] }} {% endif %}<br>
          </div>
        {% endif %}

        {% if 2 == success %}
          <div class="alert alert-success alert-dismissible" role="alert">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4>{{lang('neworder.message.success')}}</h4>
            {{lang('neworder.subscription_id')}}: {{order['id']}}<br>
            {{lang('neworder.service')}}: {{order['service']}}<br>
            {{lang('neworder.username')}}: {{order['link']}}<br>
            {{lang('neworder.quantity')}}:
            {% if order['quantity_min'] == order['quantity_max'] %}
              {{order['quantity_max']}}
            {% else %}
              {{order['quantity_min']}}-{{order['quantity_max']}}
            {% endif %}<br>
            {{lang('neworder.new_posts')}}: {{order['posts']}}<br>
            {{lang('neworder.old_posts')}}: {{order['old_posts']}}<br>
            {{lang('neworder.delay')}}: {{order['delay']}}<br>
          </div>
        {% endif %}

        <!-- Three Blue Action Buttons -->
        <div class="action-buttons">
          <div class="action-btn new-order-btn active">
            <i class="fas fa-shopping-cart"></i>
            <span>New Order</span>
          </div>
          <div class="action-btn video-tutorial-btn">
            <i class="fas fa-play"></i>
            <span>Video Tutorial</span>
          </div>
          <div class="action-btn search-orders-btn">
            <i class="fas fa-search"></i>
            <span>Search For your orders</span>
            <button class="search-icon-btn">
              <i class="fas fa-search"></i>
            </button>
          </div>
        </div>

        <!-- Form Section -->
        <div class="form-section">
          <form {% if site['rtl'] %} class="rtl-form"{% endif %} action="{{ page['url'] }}" method="post" id="order-form">

            <!-- Search Input (when search button is active) -->
            {% if services_search %}
              <div class="search-input-section" id="search-section" style="display: none;">
                <div class="search-dropdown" id="new-order-search" style="position: relative;" data-lang-placeholder="{{ lang('general.search.placeholder') }}" {% if site['rtl'] %} data-dir="rtl" {% endif %}>
                  <div class="input-wrapper" style="position: relative;">
                    <button style="position: absolute;top: 50%;transform: translateY(-50%);padding: 0px 2px;background: none;border: none;{% if site['rtl'] %}right: 12px;left: auto;{% else %}left: 12px;{% endif %}" type="button">
                        <span class="fas fa-search"></span>
                    </button>
                    <input type="text" class="form-control" id="template-input" value="" placeholder="{{ lang('general.search.placeholder') }}">
                  </div>
                </div>
              </div>
            {% endif %}

            <!-- Category Selection -->
            {% if categories %}
              <div class="form-group">
                <label for="orderform-category" class="control-label">
                  <i class="fas fa-folder"></i>
                  {{ lang('neworder.category') }}
                </label>
                <select
                    class="form-control"
                    id="orderform-category"
                    name="OrderForm[category]"
                    data-select="true"
                    data-select-search="true"
                    data-select-search-placeholder="{{ lang('general.search.placeholder') }}"
                    {% if site['rtl'] %} data-select-dir="rtl" {% endif %}
                >
                  {% for category in extended_categories %}
                    <option
                        value="{{ category.id }}"
                        {% if category.icon %}
                          data-icon="
                            {% if category.icon['icon_type'] == 'icon' %}
                              <span class='{{ category.icon['icon'] }}'></span>
                            {% elseif category.icon['icon_type'] == 'emoji' %}
                              <span>{{ category.icon['icon'] }}</span>
                            {% elseif category.icon['icon_type'] == 'image' %}
                              <img src='{{ category.icon['url'] }}' alt='{{ category.name }}' class='img-responsive btn-group-vertical'>
                            {% endif %}
                          "
                        {% endif %}
                        {% if category.id == data['category'] %} selected {% endif %}
                    >
                        {{ category.name }}
                    </option>
                  {% endfor %}
                </select>
              </div>
            {% endif %}

            <!-- Service Selection -->
            <div class="form-group">
              <label for="orderform-service" class="control-label">
                <i class="fas fa-cogs"></i>
                {{ lang('neworder.service') }}
              </label>
              <select
                class="form-control"
                id="orderform-service"
                name="OrderForm[service]"
                data-select="true"
                data-select-search="true"
                data-select-search-placeholder="{{ lang('general.search.placeholder') }}"
                {% if site['rtl'] %} data-select-dir="rtl" {% endif %}
              ></select>
            </div>

            <!-- Dynamic Fields -->
            <div id="fields"></div>

            <!-- Charge -->
            <div class="form-group">
              <label for="charge" class="control-label">{{ lang('neworder.charge') }}</label>
              <input type="text" class="form-control" id="charge" value="{{ order['charge'] }}" readonly>
            </div>

            <!-- Terms Agreement -->
            {% if check_agreement %}
              <div class="form-group">
                <label class="control-label terms">
                  <input type="checkbox"  name="OrderForm[termsofservice]" class="terms-accept-checkbox" value="1" id="terms" {{data['termsofservice'] ? 'checked' : ''}}> {{lang('signup.accept_terms_text')}}
                  <a  href="{{ page_url('terms') }}" target="_blank">{{ lang('terms.signup_checkbox') }}</a>
                </label>
              </div>
            {%endif%}

            <!-- Submit Button -->
            <input type="hidden" name="_csrf" value="{{ csrftoken }}">
            <button type="submit" class="btn btn-primary submit-button">{{ lang('neworder.button') }}</button>
          </form>
        </div>
      </div>
      <!-- Right Side - Service Details -->
      <div class="right-column">
        <div class="service-details-card">
          <div class="service-details-header">
            <i class="fas fa-info-circle"></i>
            <span>Service details</span>
          </div>

          <div class="service-details-content">
            <!-- Example Link Section -->
            <div class="example-link-section">
              <h4>Example Link</h4>
              <div class="link-display">
                <div class="link-icon">
                  <i class="fas fa-link"></i>
                </div>
                <div class="link-text" id="example-link-text">
                  Select a service to see example
                </div>
              </div>
            </div>

            <!-- Service Stats -->
            <div class="service-stats">
              <div class="stat-item">
                <div class="stat-icon speed-icon">
                  <i class="fas fa-tachometer-alt"></i>
                </div>
                <div class="stat-info">
                  <span class="stat-label">Speed</span>
                  <span class="stat-value" id="service-speed">-</span>
                </div>
              </div>

              <div class="stat-item">
                <div class="stat-icon guarantee-icon">
                  <i class="fas fa-shield-alt"></i>
                </div>
                <div class="stat-info">
                  <span class="stat-label">Guarantee</span>
                  <span class="stat-value" id="service-guarantee">-</span>
                </div>
              </div>

              <div class="stat-item">
                <div class="stat-icon time-icon">
                  <i class="fas fa-clock"></i>
                </div>
                <div class="stat-info">
                  <span class="stat-label">Average Time</span>
                  <span class="stat-value" id="service-time">-</span>
                </div>
              </div>
            </div>

            <!-- More Details Section (replaces old description) -->
            <div class="more-details-section hidden fields" id="service_description">
              <h4>More Details</h4>
              <div class="details-content" id="service-details-content">
                {{service['description']}}
              </div>
            </div>
          </div>
        </div>
      </div>
      </div>
    </div>
  </div>
</div>

<!-- Additional Content -->
{% if newOrderText %}
  <div class="additional-content">
    <div class="container-fluid">
      <div class="row">
        <div class="col-md-12">
          <div class="content-card {% if site['rtl'] %} rtl-content {% endif %}">
            {{ newOrderText }}
          </div>
        </div>
      </div>
    </div>
  </div>
{% endif %}

<style>
/* Exact Match Design CSS */
.neworder-page {
  background: #f5f5f5;
  min-height: 100vh;
  font-family: Arial, sans-serif;
}

/* Top Blue Header Bar */
.top-header-bar {
  background: #4a90e2;
  color: white;
  padding: 8px 0;
  font-size: 13px;
  border-bottom: 1px solid #357abd;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.header-content span {
  white-space: nowrap;
}

.header-content strong {
  font-weight: bold;
}

/* Platform Buttons Row */
.platform-buttons-row {
  background: white;
  padding: 15px 0;
  border-bottom: 1px solid #ddd;
}

.platform-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  flex-wrap: wrap;
}

.platform-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px 15px;
  background: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  color: #333;
  min-width: 80px;
}

.platform-btn:hover {
  background: #e3f2fd;
  border-color: #4a90e2;
  color: #4a90e2;
}

.platform-btn.active {
  background: #4a90e2;
  border-color: #4a90e2;
  color: white;
}

.platform-btn i {
  font-size: 20px;
  margin-bottom: 5px;
}

.platform-btn span {
  font-size: 11px;
  font-weight: 500;
  text-align: center;
}

/* Main Layout */
.main-layout {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.content-wrapper {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

/* Left Column */
.left-column {
  flex: 1;
  max-width: 70%;
}

/* Right Column */
.right-column {
  width: 30%;
  min-width: 300px;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: #4a90e2;
  color: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
  position: relative;
}

.action-btn:hover {
  background: #357abd;
}

.action-btn.active {
  background: #2c5aa0;
}

.action-btn i {
  font-size: 16px;
}

/* Search Orders Button Special Styling */
.search-orders-btn {
  position: relative;
  padding-right: 50px;
}

.search-icon-btn {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 8px 10px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.search-icon-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Form Section */
.form-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 20px;
}

.control-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  font-size: 14px;
}

.control-label i {
  color: #4a90e2;
  font-size: 14px;
}

.form-control {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.2s ease;
  /* CRITICAL: Ensure dropdowns are clickable */
  pointer-events: auto !important;
  position: relative !important;
  z-index: 1000 !important;
  cursor: pointer !important;
}

.form-control:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
  z-index: 1001 !important;
}

/* Ensure select dropdowns work properly */
select.form-control {
  background: white !important;
  cursor: pointer !important;
  pointer-events: auto !important;
}

select.form-control:disabled {
  pointer-events: none !important;
  cursor: not-allowed !important;
}

/* Fix any overlay blocking issues */
.form-group {
  position: relative !important;
  z-index: 100 !important;
}

/* Ensure no elements block dropdown clicks */
.neworder-page * {
  pointer-events: auto;
}

/* Specific fix for search section not blocking dropdowns */
.search-input-section {
  pointer-events: auto !important;
  z-index: 50 !important;
}

.submit-button {
  background: #4a90e2;
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.submit-button:hover {
  background: #357abd;
}

/* Service Details Card */
.service-details-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.service-details-header {
  background: #4a90e2;
  color: white;
  padding: 15px 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  font-size: 14px;
}

.service-details-content {
  padding: 20px;
}

/* Example Link Section */
.example-link-section h4 {
  color: #333;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 10px;
}

.link-display {
  display: flex;
  align-items: center;
  gap: 10px;
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #ddd;
  margin-bottom: 20px;
}

.link-icon {
  background: #4a90e2;
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.link-text {
  flex: 1;
  font-size: 12px;
  color: #666;
  word-break: break-all;
}

/* Service Stats */
.service-stats {
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px;
  margin-bottom: 10px;
  background: #f8f9fa;
  border-radius: 6px;
}

.stat-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

.speed-icon {
  background: #ff6b6b;
}

.guarantee-icon {
  background: #4ecdc4;
}

.time-icon {
  background: #45b7d1;
}

.stat-info {
  flex: 1;
}

.stat-label {
  display: block;
  font-size: 11px;
  color: #666;
  font-weight: 500;
  margin-bottom: 2px;
}

.stat-value {
  display: block;
  font-size: 12px;
  color: #333;
  font-weight: 600;
}

/* More Details Section */
.more-details-section h4 {
  color: #333;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 10px;
}

.details-content {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  font-size: 11px;
  line-height: 1.4;
  color: #555;
}

.details-content p {
  margin: 4px 0;
}

/* Alerts */
.alert {
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid transparent;
  border-radius: 4px;
}

.alert-danger {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.alert-success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }

  .platform-container {
    justify-content: flex-start;
    overflow-x: auto;
    padding-bottom: 10px;
  }

  .content-wrapper {
    flex-direction: column;
  }

  .left-column {
    max-width: 100%;
  }

  .right-column {
    width: 100%;
    min-width: auto;
  }

  .action-buttons {
    flex-wrap: wrap;
  }

  .action-btn {
    flex: 1;
    min-width: 120px;
  }
}

</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Platform button functionality
    const platformButtons = document.querySelectorAll('.platform-btn');
    const categorySelect = document.getElementById('orderform-category');
    const serviceSelect = document.getElementById('orderform-service');

    // Action button functionality
    const actionButtons = document.querySelectorAll('.action-btn');
    const searchSection = document.getElementById('search-section');

    // Platform button clicks
    platformButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            platformButtons.forEach(btn => btn.classList.remove('active'));

            // Add active class to clicked button
            this.classList.add('active');

            // Get platform type and filter categories
            const platform = this.dataset.platform;
            filterCategoriesByPlatform(platform);
        });
    });

    // Action button clicks
    actionButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all action buttons
            actionButtons.forEach(btn => btn.classList.remove('active'));

            // Add active class to clicked button
            this.classList.add('active');

            // Handle specific button actions
            if (this.classList.contains('search-orders-btn')) {
                toggleSearchSection();
            } else if (this.classList.contains('video-tutorial-btn')) {
                openVideoTutorial();
            }
        });
    });

    // Service selection functionality
    if (serviceSelect) {
        serviceSelect.addEventListener('change', function() {
            updateServiceDetails(this.value);
        });
    }

    function filterCategoriesByPlatform(platform) {
        if (!categorySelect) return;

        const options = categorySelect.querySelectorAll('option');
        options.forEach(option => {
            if (platform === 'everything') {
                option.style.display = '';
            } else {
                const categoryName = option.textContent.toLowerCase();
                if (categoryName.includes(platform.toLowerCase()) || option.value === '') {
                    option.style.display = '';
                } else {
                    option.style.display = 'none';
                }
            }
        });

        // Reset service selection
        if (serviceSelect) {
            serviceSelect.innerHTML = '<option value="">Select a service...</option>';
        }

        // Reset service details
        resetServiceDetails();
    }

    function toggleSearchSection() {
        if (searchSection) {
            if (searchSection.style.display === 'none') {
                searchSection.style.display = 'block';
            } else {
                searchSection.style.display = 'none';
            }
        }
    }

    function openVideoTutorial() {
        // This would open a video tutorial modal or redirect
        alert('Video tutorial feature coming soon!');
    }

    function updateServiceDetails(serviceId) {
        if (!serviceId) {
            resetServiceDetails();
            return;
        }

        // Update example link and service stats
        document.getElementById('example-link-text').textContent = 'https://www.youtube.com/watch?v=example';
        document.getElementById('service-speed').textContent = 'Fast';
        document.getElementById('service-guarantee').textContent = 'Will No';
        document.getElementById('service-time').textContent = '0';

        // Don't update the service details content via JavaScript
        // Let the backend/Twig template handle the service description display
    }

    function resetServiceDetails() {
        document.getElementById('example-link-text').textContent = 'Select a service to see example';
        document.getElementById('service-speed').textContent = '-';
        document.getElementById('service-guarantee').textContent = '-';
        document.getElementById('service-time').textContent = '-';
        // Don't reset the service details content - let Twig template handle it
    }

    // Initialize with "Everything" platform selected
    const everythingBtn = document.querySelector('[data-platform="everything"]');
    if (everythingBtn) {
        everythingBtn.click();
    }

    // Initialize with "New Order" action button selected
    const newOrderBtn = document.querySelector('.new-order-btn');
    if (newOrderBtn) {
        newOrderBtn.classList.add('active');
    }
});
</script>

