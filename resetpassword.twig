{% if site['resetPassword'] %}
  <div class="container">
    <div class="row">
      <div class="col-md-8 col-md-offset-2">
        <div class="well">
          <form {% if site['rtl'] %}class="rtl-form"{% endif %} method="post" action="">
            <h3>{{  lang('resetpassword.title') }}</h3>
            {% if error %}
              <div class="alert alert-dismissible alert-danger {% if site['rtl'] %} rtl-alert {% endif %}">
                <button type="button" class="close" data-dismiss="alert">&times;</button>
                {{ errorMessage }}
              </div>
            {% endif %}
            {% if success %}
              <div class="alert alert-dismissible alert-success {% if site['rtl'] %} rtl-alert {% endif %}">
                <button type="button" class="close" data-dismiss="alert">&times;</button>
                {{ successText }}
              </div>
            {% endif %}
            <div class="form-group">
              <label for="email" class="control-label">{{ lang('resetpassword.email') }}</label>
              <input type="email" class="form-control" id="email" name="ResetPasswordForm[email]">
            </div>
            {% if site['captcha'] %}
              {{ captchaCode }}
            {% endif %}
            <button type="submit" class="btn btn-primary">{{ lang('resetpassword.button.send') }}</button>
            <input type="hidden" name="_csrf" value="{{csrftoken}}">
          </form>
        </div>
      </div>
    </div>
  </div>
{% endif %}

{% if site['captcha'] %}
  <script src='https://www.google.com/recaptcha/api.js?hl={{ site['iso_lang_code'] }}'></script>
{% endif %}

