<div class="container-fluid">
  <div class="row">
    <div class="col-lg-12">
      <ul class="nav nav-pills {% if site['rtl'] %} rtl-nav {% endif %}">
        <li {% if 'all' == status %}class="active"{% endif %}><a href="{{ page['url'] }}">{{ lang('orders.all') }}</a></li>
        <li {% if 'pending' == status %}class="active"{% endif %}><a href="{{ page['url'] }}/pending">{{ lang('orders.status.pending') }}</a></li>
        <li {% if 'inprogress' == status %}class="active"{% endif %}><a href="{{ page['url'] }}/inprogress">{{ lang('orders.status.inprogress') }}</a></li>
        <li {% if 'completed' == status %}class="active"{% endif %}><a href="{{ page['url'] }}/completed">{{ lang('orders.status.completed') }}</a></li>
        <li {% if 'partial' == status %}class="active"{% endif %}><a href="{{ page['url'] }}/partial">{{ lang('orders.status.partial') }}</a></li>
        <li {% if 'processing' == status %}class="active"{% endif %}><a href="{{ page['url'] }}/processing">{{ lang('orders.status.processing') }}</a></li>
        <li {% if 'canceled' == status %}class="active"{% endif %}><a href="{{ page['url'] }}/canceled">{{ lang('orders.status.canceled') }}</a></li>
        <li class="pull-right search">
          <form action="{{ page['url'] }}" method="get" id="history-search">
            <div class="input-group">
              <input type="text" name="search" class="form-control" value="{{ search }}"
                     placeholder="{{ lang('general.search.placeholder') }}">
              <span class="input-group-btn">
                <button type="submit" class="btn btn-default"><i class="fa fa-search" aria-hidden="true"></i></button>
              </span>
            </div>
          </form>
        </li>
      </ul>
      <div class="well well-float">
        <table class="table {% if site['rtl'] %} rtl-table {% endif %}">
          <thead>
          <tr>
            <th>{{ lang('orders.id') }}</th>
            <th>{{ lang('orders.date') }}</th>
            <th class="width-40">{{ lang('orders.link') }}</th>
            <th>{{ lang('orders.charge') }}</th>
            <th class="nowrap">{{ lang('orders.startcount') }}</th>
            <th>{{ lang('orders.quantity') }}</th>
            <th>{{ lang('orders.service') }}</th>
            <th>{{ lang('orders.status') }}</th>
            <th>{{ lang('orders.remains') }}</th>
            {% if task == 1 %}
              <th>&nbsp;</th>{% endif %}
          </tr>
          </thead>
          <tbody>
          {% for order in orderList %}
            <tr>
              <td>{{ order['id'] }}</td>
              <td>{{ order['date'] }}</td>
              <td class="width-40">
                {{ order['link'] }}
                {% if order['orderDetails'] %}
                  {% for orderDetails in order['orderDetails'] %}
                    <a href="#" data-toggle="modal" data-target="#order-details-{{ order['id'] }}-{{ loop.index }}">
                      <i class="far fa-file-alt"></i>
                    </a>
                    <div class="modal fade {% if site.rtl %}modal-rtl{% endif %}" tabindex="-1" role="dialog" id="order-details-{{ order['id'] }}-{{ loop.index }}">
                      <div class="modal-dialog" role="document">
                        <div class="modal-content">
                          <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title">{{ orderDetails.label }}</h4>
                          </div>
                          <div class="modal-body">
                            {% for orderDetail in orderDetails['details'] %}
                              <div class="form-group">
                                <label for="details-{{ order['id'] }}-{{ loop.index }}">{{ orderDetail.label }}</label>
                                {% if orderDetail.type == 'textarea' %}
                                  <textarea id="details-{{ order['id'] }}-{{ loop.index }}" class="form-control" rows="7" disabled>{{ orderDetail.value }}</textarea>
                                {% elseif orderDetail.type == 'input' %}
                                  <input id="details-{{ order['id'] }}-{{ loop.index }}" class="form-control" value="{{ orderDetail.value }}" disabled />
                                {% endif %}
                              </div>
                            {% endfor %}
                          </div>
                        </div>
                      </div>
                    </div>
                  {% endfor %}
                {% endif %}
              </td>
              <td>{{ order['charge'] }}</td>
              <td class="nowrap">{{ order['start_count'] }}</td>
              <td>{{ order['quantity'] }}</td>
              <td>{{ order['service'] }}</td>
              <td class="nowrap">{{ order['status'] }} {% if order['cancelReason'] %}<i class="fas fa-comment-alt-lines" data-toggle="tooltip" data-placement="top" title="{{ order['cancelReason'] }}"></i>{% endif %}</td>
              <td>{{ order['remains'] }}</td>
              {% if task == 1 %}
              <td>
                <div class="order-actions">
                  {% if order['refilling'] == 1 %}
                    {{ lang('orders.refilling') }}
                  {% else %}
                    {% if order['refill'] == 1 %}
                      <a href="{{ page['url'] }}/{{ order['id'] }}/refill" class="btn btn-xs btn-primary">{{ lang('orders.button.refill') }}</a>
                    {% endif %}
                    {% if order['refillAvailableTime'] %}
                      <button class="btn btn-xs btn-primary disabled" data-toggle="tooltip" data-placement="top" title="{{ order['refillAvailableTime'] }}">{{ lang('orders.button.refill') }}</button>
                    {% endif %}
                    {% if order['cancel'] == 1 %}<a href="{{ page['url'] }}/{{ order['id'] }}/cancel" class="btn btn-xs btn-default">{{ lang('orders.button.cancel') }}</a>{% endif %}
                    {% if order['hasCancelTask'] == 1 %}
                      {{ lang('orders.cancel_requested') }}
                    {% endif %}
                  {% endif %}
                </div>
              </td>
              {% endif %}
            </tr>
          {% endfor %}
          </tbody>
        </table>
      </div>

      {% if pagination['count'] > 100 %}
        {% if searchList %}
          {% set params = {} %}
          {% for search in searchList %}
            {% set params = params | merge([search['name'] ~ '=' ~ search['value']]) %}
          {% endfor %}
          {% set params = '?' ~ params|join('&') %}
        {% endif %}
        <ul class="pagination {% if site['rtl'] %} rtl-pagination {% endif %}">
          {% if pagination['current'] != 1 %}
            <li>
            <a href="{{ page['url'] }}/{{ status }}/{{ pagination['last'] }}{{ params }}" aria-label="Previous">
              <span aria-hidden="true">&laquo;</span>
            </a>
            </li>
          {% endif %}

          {% set r, l = 3, 3 %}

          {% if pagination['current'] == 1 %}
            {% set r = 6 %}
          {% endif %}

          {% if pagination['current'] == 2 %}
            {% set r = 5 %}
          {% endif %}

          {% if pagination['current'] >= pagination['pages'] %}
            {% set l = 5 %}
          {% endif %}

          {% for i in 1..ceil(pagination['pages']) %}
            {% if i >= (pagination['current']-l) and i <= (pagination['current']+r) %}
              <li{% if i == pagination['current'] %} class="active"{% endif %}><a
                    href="{{ page['url'] }}/{{ status }}/{{ i }}{{ params }}">{{ i }}</a></li>
            {% endif %}
          {% endfor %}

          {% if pagination['current'] < pagination['pages'] %}
            <li>
              <a href="{{ page['url'] }}/{{ status }}/{{ pagination['next'] }}{{ params }}" aria-label="Next">
                <span aria-hidden="true">&raquo;</span>
              </a>
            </li>
          {% endif %}
        </ul>
      {% endif %}

    </div>
  </div>
</div>
