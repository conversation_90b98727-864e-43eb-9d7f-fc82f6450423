<!DOCTYPE html>
<html lang="{{site['iso_lang_code']}}">
	<head>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, initial-scale=1">
		<title>{{ page['title'] }}</title>
		<meta name="keywords" content="{{ site['seo_key'] }}">
		<meta name="description" content="{{ site['seo_desc'] }}">
		{% if site['favicon'] %}
			<link rel="shortcut icon" type="image/ico" href="{{ site['favicon'] }}"/>
		{% endif %}

		<!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
		<!--[if lt IE 9]>
		  <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
		  <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
		  <![endif]-->
		{{ site['custom_header'] }}

		{% for style in site['styles'] %}
			<link rel="stylesheet" type="text/css" href="{{ style['href'] }}">
		{% endfor %}

		<!-- Font Awesome for Icons -->
		<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

		<!-- Header and Footer Styles for Non-Logged-In Users -->
		<style>
		/* Header Styles */
		.khmer-header {
			background: #ffffff;
			border-bottom: 1px solid #e8e8e8;
			padding: 10px 0;
			position: sticky;
			top: 0;
			z-index: 1000;
			box-shadow: 0 1px 3px rgba(0,0,0,0.1);
		}

		.header-content {
			display: flex;
			align-items: center;
			justify-content: space-between;
			max-width: 1200px;
			margin: 0 auto;
			padding: 0 20px;
		}

		.header-logo .logo-link {
			display: flex;
			align-items: center;
			text-decoration: none;
			color: inherit;
		}

		.header-logo .logo-container {
			display: flex;
			align-items: center;
			background: linear-gradient(135deg, #1e88e5 0%, #2196f3 100%);
			padding: 8px 16px;
			border-radius: 12px;
			box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
			transition: all 0.3s ease;
		}

		.header-logo .logo-container:hover {
			transform: translateY(-2px);
			box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
		}

		.header-logo .logo-icon {
			font-size: 24px;
			margin-right: 10px;
			color: #ffffff;
			filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
		}

		.header-logo .logo-text {
			font-size: 20px;
			font-weight: 800;
			color: #ffffff;
			text-transform: uppercase;
			letter-spacing: 1px;
			text-shadow: 0 2px 4px rgba(0,0,0,0.2);
			font-family: 'Arial Black', Arial, sans-serif;
		}

		.header-logo .logo-text .chhean {
			color: #ffffff;
		}

		.header-logo .logo-text .smm {
			color: #ffeb3b;
			margin-left: 4px;
		}

		.header-nav {
			flex: 1;
			display: flex;
			justify-content: center;
		}

		.nav-menu {
			display: flex;
			list-style: none;
			margin: 0;
			padding: 0;
			gap: 25px;
			align-items: center;
		}

		.nav-menu li a {
			color: #666;
			text-decoration: none;
			font-size: 13px;
			font-weight: 500;
			padding: 6px 0;
			transition: all 0.3s ease;
			position: relative;
		}

		.nav-menu li a:hover {
			color: #2196f3;
			text-decoration: none;
		}

		.nav-menu li a:hover::after {
			content: '';
			position: absolute;
			bottom: -2px;
			left: 0;
			right: 0;
			height: 2px;
			background: #2196f3;
		}

		.header-signup .signup-btn {
			background: #2196f3;
			color: white;
			text-decoration: none;
			padding: 8px 16px;
			border-radius: 5px;
			font-size: 13px;
			font-weight: 600;
			display: flex;
			align-items: center;
			gap: 5px;
			transition: all 0.3s ease;
			border: none;
			cursor: pointer;
		}

		.header-signup .signup-btn:hover {
			background: #1976d2;
			color: white;
			text-decoration: none;
		}

		.header-signup .signup-btn i {
			font-size: 12px;
		}

		.mobile-menu-toggle {
			display: none;
			flex-direction: column;
			cursor: pointer;
			gap: 4px;
		}

		.mobile-menu-toggle span {
			width: 25px;
			height: 3px;
			background: #333;
			border-radius: 2px;
			transition: all 0.3s ease;
		}

		/* Mobile Responsive */
		@media (max-width: 768px) {
			.header-nav {
				display: none;
			}

			.mobile-menu-toggle {
				display: flex;
			}

			.header-logo .logo-text {
				font-size: 18px;
			}

			.header-signup .signup-btn {
				padding: 8px 16px;
				font-size: 13px;
			}
		}
		/* Footer Styles */
		.footer-section {
			background: linear-gradient(135deg, #1a2332 0%, #2c3e50 100%);
			color: #ffffff;
			padding: 60px 0 25px 0;
			margin-top: 80px;
			margin-bottom: 0;
			width: 100vw;
			position: relative;
			left: 50%;
			right: 50%;
			margin-left: -50vw;
			margin-right: -50vw;
		}

		.footer-brand {
			margin-bottom: 30px;
		}

		.footer-logo {
			display: flex;
			align-items: center;
			margin-bottom: 20px;
		}

		.footer-logo .logo-container {
			display: flex;
			align-items: center;
			background: linear-gradient(135deg, #1e88e5 0%, #2196f3 100%);
			padding: 12px 20px;
			border-radius: 15px;
			box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
		}

		.footer-logo .logo-icon {
			font-size: 32px;
			margin-right: 12px;
			color: #ffffff;
			filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
		}

		.footer-logo .logo-text {
			font-size: 28px;
			font-weight: 800;
			color: #ffffff;
			text-transform: uppercase;
			letter-spacing: 1px;
			text-shadow: 0 2px 4px rgba(0,0,0,0.2);
			font-family: 'Arial Black', Arial, sans-serif;
		}

		.footer-logo .logo-text .chhean {
			color: #ffffff;
		}

		.footer-logo .logo-text .smm {
			color: #ffeb3b;
			margin-left: 4px;
		}

		.footer-description {
			color: #b8c5d1;
			font-size: 14px;
			line-height: 1.6;
			margin-bottom: 25px;
		}

		.social-media-icons {
			display: flex;
			gap: 12px;
		}

		.social-icon {
			width: 40px;
			height: 40px;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			color: white;
			text-decoration: none;
			transition: all 0.3s ease;
			font-size: 16px;
		}

		.social-icon:hover {
			transform: translateY(-3px);
			box-shadow: 0 5px 15px rgba(0,0,0,0.3);
			color: white;
			text-decoration: none;
		}

		.social-icon.facebook { background-color: #1877f2; }
		.social-icon.youtube { background-color: #ff0000; }
		.social-icon.twitter { background-color: #1da1f2; }
		.social-icon.telegram { background-color: #0088cc; }
		.social-icon.instagram { background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%); }
		.social-icon.skype { background-color: #00aff0; }

		.footer-title {
			color: #ffffff;
			font-size: 18px;
			font-weight: 600;
			margin-bottom: 20px;
			text-transform: uppercase;
			letter-spacing: 0.5px;
		}

		.footer-menu {
			list-style: none;
			padding: 0;
			margin: 0;
		}

		.footer-menu li {
			margin-bottom: 12px;
		}

		.footer-menu a {
			color: #b8c5d1;
			text-decoration: none;
			font-size: 14px;
			transition: color 0.3s ease;
		}

		.footer-menu a:hover {
			color: #4a9eff;
		}

		.footer-contact {
			margin-top: 10px;
		}

		.contact-item {
			display: flex;
			align-items: flex-start;
			margin-bottom: 20px;
		}

		.contact-icon {
			width: 40px;
			height: 40px;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: 15px;
			flex-shrink: 0;
			background-color: #4a9eff;
			color: white;
		}

		.contact-info p {
			margin: 0;
			color: #b8c5d1;
			font-size: 14px;
			line-height: 1.5;
		}

		.contact-info .telegram-link {
			color: #4a9eff;
			text-decoration: none;
			font-weight: 500;
		}

		.contact-info .telegram-link:hover {
			color: #ffffff;
		}

		.footer-bottom {
			border-top: 1px solid #34495e;
			margin-top: 40px;
			margin-bottom: 0;
			padding: 25px 0;
		}

		.footer-copyright {
			text-align: center;
		}

		.footer-copyright p {
			margin: 0;
			color: #b8c5d1;
			font-size: 14px;
		}

		/* Mobile Responsive */
		@media (max-width: 768px) {
			.footer-section {
				padding: 40px 0 0;
			}

			.social-media-icons {
				justify-content: center;
			}

			.footer-menu {
				text-align: center;
			}

			.contact-item {
				justify-content: center;
				text-align: left;
			}
		}
		</style>

		<script>
			window.modules = {};
		</script>
	</head>
	<body>
		<!-- Main content area - this is where all templates render -->
		{{ content }}

		<!-- Styles for all users -->
		<style>
			/* Header Styles */
			.auth-header {
				position: fixed !important;
				top: 0 !important;
				left: 0 !important;
				right: 0 !important;
				width: 100% !important;
				height: 50px !important;
				background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%) !important;
				display: flex !important;
				align-items: center !important;
				justify-content: space-between !important;
				padding: 0 20px !important;
				z-index: 9999 !important;
				box-shadow: 0 2px 8px rgba(74, 144, 226, 0.2) !important;
				visibility: visible !important;
				opacity: 1 !important;
				transition: all 0.3s ease !important;
			}

			/* Header adjusts when sidebar is open */
			.sidebar-open .auth-header {
				left: 280px !important;
				width: calc(100% - 280px) !important;
			}

			.header-left {
				display: flex !important;
				align-items: center !important;
			}

			.header-left .sidebar-toggle {
				background: rgba(255, 255, 255, 0.2) !important;
				border: none !important;
				color: white !important;
				font-size: 18px !important;
				cursor: pointer !important;
				padding: 8px 12px !important;
				border-radius: 6px !important;
				transition: background 0.3s ease !important;
			}

			.header-left .sidebar-toggle:hover {
				background: rgba(255, 255, 255, 0.3) !important;
			}

			.header-right {
				display: flex !important;
				align-items: center !important;
				gap: 15px !important;
			}

			.support-btn {
				display: flex !important;
				align-items: center !important;
				justify-content: center !important;
				width: 40px !important;
				height: 40px !important;
				background: rgba(255, 255, 255, 0.2) !important;
				border-radius: 8px !important;
				text-decoration: none !important;
				transition: all 0.3s ease !important;
			}

			.support-btn:hover {
				background: rgba(255, 255, 255, 0.3) !important;
				transform: scale(1.05) !important;
			}

			.support-btn img {
				width: 24px !important;
				height: 24px !important;
				object-fit: contain !important;
			}

			.profile-dropdown {
				position: relative !important;
			}

			.profile-btn {
				background: none !important;
				border: none !important;
				cursor: pointer !important;
				padding: 0 !important;
				display: flex !important;
				align-items: center !important;
				justify-content: center !important;
			}

			.profile-btn img {
				width: 40px !important;
				height: 40px !important;
				border-radius: 50% !important;
				border: 2px solid rgba(255, 255, 255, 0.3) !important;
				transition: all 0.3s ease !important;
				object-fit: cover !important;
			}

			.profile-btn:hover img {
				border-color: rgba(255, 255, 255, 0.6) !important;
				transform: scale(1.05) !important;
			}

			.dropdown-menu {
				position: absolute;
				top: 45px;
				right: 0;
				background: white;
				border-radius: 8px;
				box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
				min-width: 200px;
				opacity: 0;
				visibility: hidden;
				transform: translateY(-10px);
				transition: all 0.3s ease;
				z-index: 1070;
			}

			.dropdown-menu.show {
				opacity: 1;
				visibility: visible;
				transform: translateY(0);
			}

			.dropdown-header {
				padding: 15px;
				border-bottom: 1px solid #eee;
			}

			.user-name {
				font-size: 14px;
				font-weight: 600;
				color: #333;
				margin-bottom: 3px;
			}

			.user-balance {
				font-size: 12px;
				color: #666;
			}

			.dropdown-item {
				display: flex;
				align-items: center;
				gap: 10px;
				padding: 12px 15px;
				color: #333;
				text-decoration: none;
				font-size: 13px;
				transition: background 0.3s ease;
			}

			.dropdown-item:hover {
				background: #f5f5f5;
				text-decoration: none;
				color: #333;
			}

			.dropdown-item.logout:hover {
				background: #fee;
				color: #e74c3c;
			}

			.dropdown-item i {
				font-size: 14px;
				width: 16px;
			}

			/* Sidebar Styles - Fill from top to bottom with smooth animations */
			.user-sidebar {
				position: fixed !important;
				top: 0 !important;
				left: 0 !important;
				bottom: 0 !important;
				width: 280px !important;
				height: 100vh !important;
				background: linear-gradient(180deg, #34495e 0%, #2c3e50 100%) !important;
				color: white !important;
				transform: translateX(-280px) !important;
				transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), visibility 0.3s ease, opacity 0.3s ease !important;
				z-index: 1040 !important;
				overflow: hidden !important;
				border: none !important;
				margin: 0 !important;
				padding: 0 !important;
				box-sizing: border-box !important;
				display: flex !important;
				flex-direction: column !important;
				visibility: hidden !important;
				opacity: 0 !important;
			}

			.user-sidebar.show {
				transform: translateX(0) !important;
				visibility: visible !important;
				opacity: 1 !important;
			}

			/* Ensure closed state is properly hidden with smooth transition */
			.user-sidebar:not(.show) {
				transform: translateX(-280px) !important;
				visibility: hidden !important;
				opacity: 0 !important;
			}

			/* Prevent any blinking during transitions */
			.user-sidebar * {
				transition: inherit !important;
			}

			/* FORCE: Remove any top gaps in sidebar */
			.user-sidebar .sidebar-header:first-child {
				margin-top: 0 !important;
				padding-top: 15px !important;
			}

			/* Ensure sidebar fills from absolute top */
			.user-sidebar {
				padding-top: 0 !important;
				margin-top: 0 !important;
			}

			/* Sidebar overlay */
			.sidebar-overlay {
				position: fixed !important;
				top: 0 !important;
				left: 0 !important;
				width: 100% !important;
				height: 100% !important;
				background: rgba(0, 0, 0, 0.5) !important;
				z-index: 1040 !important;
				opacity: 0 !important;
				visibility: hidden !important;
				transition: all 0.3s ease !important;
			}

			.sidebar-overlay.show {
				opacity: 1 !important;
				visibility: visible !important;
			}

			/* Hide the old navbar */
			.navbar.navbar-default.navbar-static-top {
				display: none !important;
			}

			/* Body and main content adjustments */
			body {
				padding-top: 50px;
			}

			.main-wrapper {
				transition: margin-left 0.3s ease !important;
				margin-left: 0 !important;
				min-height: calc(100vh - 50px) !important;
			}

			.main-wrapper.sidebar-open {
				margin-left: 280px !important;
			}

			/* Desktop behavior - sidebar pushes content and header */
			@media (min-width: 769px) {
				.main-wrapper.sidebar-open {
					margin-left: 280px !important;
				}

				/* Content area styles */
				.content-area {
					padding: 20px !important;
					min-height: calc(100vh - 70px) !important;
					background: #f8f9fa !important;
				}

				/* Non-auth layout */
				.non-auth-layout {
					min-height: 100vh !important;
					background: #f8f9fa !important;
				}

				.sidebar-open .auth-header {
					left: 280px !important;
					width: calc(100% - 280px) !important;
				}

				.sidebar-overlay {
					display: none !important;
				}
			}

			/* Mobile behavior - overlay mode, header stays full width */
			@media (max-width: 768px) {
				.main-wrapper.sidebar-open {
					margin-left: 0 !important;
				}

				.sidebar-open .auth-header {
					left: 0 !important;
					width: 100% !important;
				}

				body.sidebar-open {
					overflow: hidden !important;
				}
			}

			/* Ensure content is not blocked when sidebar is closed */
			body:not(.sidebar-open) .sidebar-overlay {
				pointer-events: none !important;
				opacity: 0 !important;
				visibility: hidden !important;
			}

			.sidebar-header {
				background: linear-gradient(180deg, #34495e 0%, #2c3e50 100%) !important;
				padding: 15px 20px 20px 20px !important;
				display: flex !important;
				align-items: center !important;
				gap: 12px !important;
				margin: 0 !important;
				border: none !important;
				flex-shrink: 0 !important;
				min-height: 80px !important;
				margin-top: 0 !important;
				position: relative !important;
				top: 0 !important;
			}

			.sidebar-header .logo-icon {
				width: 40px;
				height: 40px;
				background: white;
				border-radius: 8px;
				display: flex;
				align-items: center;
				justify-content: center;
				color: #2196f3;
				font-size: 20px;
				font-weight: bold;
			}

			.sidebar-header .logo-content {
				flex: 1;
			}

			.sidebar-header .logo-text {
				font-size: 22px;
				font-weight: 800;
				color: #ffffff;
				text-transform: uppercase;
				letter-spacing: 1px;
				font-family: 'Arial Black', Arial, sans-serif;
				margin: 0;
				line-height: 1.2;
			}

			.sidebar-header .logo-text .khmer {
				color: #ffffff;
			}

			.sidebar-header .logo-text .smm {
				color: #ffffff;
				margin-left: 4px;
			}

			.sidebar-header .logo-subtitle {
				font-size: 13px;
				color: rgba(255, 255, 255, 0.9);
				text-transform: uppercase;
				margin: 2px 0 0 0;
				letter-spacing: 0.5px;
				line-height: 1.2;
			}

			.sidebar-content {
				padding: 0 !important;
				flex: 1 !important;
				display: flex !important;
				flex-direction: column !important;
				min-height: calc(100vh - 80px) !important;
				overflow-y: auto !important;
				background: linear-gradient(180deg, #34495e 0%, #2c3e50 100%) !important;
				margin: 0 !important;
				border: none !important;
			}

			/* NUCLEAR FIX: Force sidebar to fill entire screen height */
			aside#userSidebar.user-sidebar {
				top: 0 !important;
				height: 100vh !important;
				min-height: 100vh !important;
				max-height: 100vh !important;
				z-index: 1040 !important;
			}

			aside#userSidebar.user-sidebar .sidebar-header {
				background: linear-gradient(180deg, #34495e 0%, #2c3e50 100%) !important;
				padding: 15px 20px 20px 20px !important;
				min-height: 80px !important;
				margin: 0 !important;
				border: none !important;
				position: relative !important;
				top: 0 !important;
			}

			aside#userSidebar.user-sidebar .sidebar-content {
				background: linear-gradient(180deg, #34495e 0%, #2c3e50 100%) !important;
				min-height: calc(100vh - 80px) !important;
				padding-bottom: 20px !important;
				margin: 0 !important;
				border: none !important;
			}

			.user-card {
				background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
				margin: 0 20px 30px 20px;
				padding: 25px 20px;
				border-radius: 15px;
				position: relative;
				overflow: hidden;
				color: white;
				box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
			}

			.user-card .new-badge {
				position: absolute;
				top: 0;
				right: 0;
				background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
				color: white;
				padding: 8px 15px;
				border-radius: 0 15px 0 15px;
				font-size: 12px;
				font-weight: 700;
				text-transform: uppercase;
				letter-spacing: 0.5px;
				box-shadow: 0 2px 8px rgba(231, 76, 60, 0.4);
			}

			.user-card .user-avatar-large {
				width: 80px;
				height: 80px;
				border-radius: 50%;
				margin: 0 auto 20px auto;
				overflow: hidden;
				display: block;
				background: rgba(255, 255, 255, 0.2);
				border: 3px solid rgba(255, 255, 255, 0.3);
			}

			.user-card .user-avatar-large img {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}

			.user-card .balance {
				font-size: 18px;
				font-weight: 700;
				margin-bottom: 8px;
				color: white;
				line-height: 1.3;
				text-align: center;
			}

			.user-card .username {
				font-size: 14px;
				color: rgba(255, 255, 255, 0.95);
				font-weight: 500;
				text-align: center;
			}

			.sidebar-menu {
				list-style: none;
				padding: 0;
				margin: 0;
			}

			.sidebar-menu li {
				margin-bottom: 5px;
			}



			.new-order-btn i {
				font-size: 18px;
				color: white;
			}

			/* Regular Menu Items */
			.sidebar-menu {
				padding: 0;
				margin: 0;
				list-style: none;
			}

			.sidebar-menu li {
				margin: 0;
			}

			.sidebar-menu a {
				display: flex;
				align-items: center;
				gap: 12px;
				padding: 12px 20px;
				color: rgba(255, 255, 255, 0.85);
				text-decoration: none;
				transition: all 0.3s ease;
				font-size: 14px;
				font-weight: 500;
				margin: 0 20px;
				border-radius: 8px;
				position: relative;
			}

			.sidebar-menu a:hover {
				background: rgba(255, 255, 255, 0.1);
				color: white;
				text-decoration: none;
			}

			/* Active state - blue highlight for current page */
			.sidebar-menu a.active {
				background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%) !important;
				color: white !important;
				text-decoration: none;
				box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
				border-left: 4px solid #ffffff;
			}

			.sidebar-menu a i {
				font-size: 16px;
				width: 18px;
				text-align: center;
				color: rgba(255, 255, 255, 0.85);
			}

			.sidebar-menu a.active i {
				color: white !important;
			}

			.sidebar-menu .badge {
				background: #f39c12;
				color: white;
				padding: 3px 7px;
				border-radius: 10px;
				font-size: 11px;
				margin-left: auto;
				font-weight: 700;
				min-width: 18px;
				text-align: center;
			}

			.show-more-btn {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 12px 20px;
				background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
				color: white;
				text-decoration: none;
				margin: 15px 15px 20px 15px;
				border-radius: 8px;
				transition: all 0.3s ease;
				font-size: 14px;
				font-weight: 600;
				box-shadow: 0 2px 10px rgba(149, 165, 166, 0.4);
			}

			.show-more-btn:hover {
				background: linear-gradient(135deg, #7f8c8d 0%, #6c7b7d 100%);
				color: white;
				text-decoration: none;
				transform: translateY(-1px);
				box-shadow: 0 4px 15px rgba(149, 165, 166, 0.5);
			}

			.show-more-btn i {
				font-size: 14px;
				transition: transform 0.3s ease;
			}

			.show-more-btn.expanded i {
				transform: rotate(180deg);
			}

			/* Additional Menu Items (hidden by default) */
			.additional-menu {
				max-height: 0;
				overflow: hidden;
				transition: max-height 0.3s ease;
			}

			.additional-menu.show {
				max-height: 400px;
			}

			/* Ensure additional menu items follow same rules */
			.additional-menu .sidebar-menu a {
				background: transparent !important;
				color: rgba(255, 255, 255, 0.85) !important;
			}

			.additional-menu .sidebar-menu a:hover {
				background: rgba(255, 255, 255, 0.1) !important;
				color: white !important;
			}

			/* Only show blue when actually active */
			.additional-menu .sidebar-menu a.active {
				background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%) !important;
				color: white !important;
				border-left: 4px solid #ffffff;
				box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
			}

			/* Smaller sidebar toggle button positioned at top */
			.navbar .sidebar-toggle-btn {
				background: none !important;
				border: none !important;
				color: #333 !important;
				font-size: 14px !important;
				padding: 6px 8px !important;
				margin: 0 !important;
				cursor: pointer !important;
				border-radius: 4px !important;
				transition: all 0.3s ease !important;
				position: absolute !important;
				top: 8px !important;
				left: 10px !important;
				width: 32px !important;
				height: 32px !important;
				display: flex !important;
				align-items: center !important;
				justify-content: center !important;
				z-index: 1060 !important;
			}

			.navbar .sidebar-toggle-btn:hover {
				background: #f8f9fa !important;
				color: #2196f3 !important;
			}

			/* Ensure button is visible when sidebar is open */
			body.sidebar-open .navbar .sidebar-toggle-btn {
				position: fixed !important;
				z-index: 1070 !important;
				background: rgba(255,255,255,0.9) !important;
				color: #333 !important;
			}

			/* Overlay - only show on mobile */
			.sidebar-overlay {
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background: rgba(0, 0, 0, 0.5);
				z-index: 1049;
				opacity: 0;
				visibility: hidden;
				transition: all 0.3s ease;
				pointer-events: none;
				display: none;
			}

			/* Mobile responsive */
			@media (max-width: 768px) {
				.sidebar-overlay {
					display: block;
				}

				.sidebar-overlay.show {
					opacity: 1;
					visibility: visible;
					pointer-events: all;
				}

				.main-wrapper.sidebar-open {
					margin-left: 0;
				}

				/* Prevent body scroll on mobile when sidebar is open */
				body.sidebar-open {
					overflow: hidden;
				}
			}

			/* Adjust navbar for sidebar */
			.navbar .container-fluid {
				padding-left: 15px;
			}

			/* Fix for double dropdown issue */
			.sidebar-overlay {
				pointer-events: none !important;
			}

			.sidebar-overlay.show {
				pointer-events: auto !important;
			}

			/* Keep the first working dropdowns, hide later broken ones */
			select[data-select="true"].duplicate-dropdown {
				display: none !important;
			}

			/* Ensure Select2 dropdowns are properly positioned */
			.select2-container {
				z-index: 9999 !important;
				position: relative !important;
			}

			.select2-dropdown {
				z-index: 9999 !important;
			}

			/* Fix for form groups with Select2 */
			.form-group .select2-container {
				width: 100% !important;
			}
			</style>





			<!-- Header for Authenticated Users -->
			<header class="auth-header">
				<div class="header-left">
					<button class="sidebar-toggle" onclick="toggleSidebar()">
						<i class="fas fa-bars"></i>
					</button>
				</div>

				<div class="header-right">
					<!-- Support Button -->
					<a href="https://t.me/chheansmm_support" target="_blank" class="support-btn">
						<img src="https://storage.perfectcdn.com/n675sa/tgfd77uw6l3rw6og.png" alt="Support">
					</a>

					<!-- Profile Dropdown -->
					<div class="profile-dropdown">
						<button class="profile-btn" onclick="toggleProfileDropdown()">
							<img src="https://storage.perfectcdn.com/n675sa/ugh7qnr2n0u93uky.png" alt="Profile">
						</button>
						<div class="dropdown-menu" id="profileDropdown">
							<div class="dropdown-header">
								<div class="user-name">{{ user.username }}</div>
								<div class="user-balance">Balance: {{ user.balance_formatted }}</div>
							</div>
							<a href="account.twig" class="dropdown-item">
								<i class="fas fa-user"></i>
								My Account
							</a>
							<a href="{{ page_url('logout') }}" class="dropdown-item logout">
								<i class="fas fa-sign-out-alt"></i>
								Logout
							</a>
						</div>
					</div>
				</div>
			</header>

			<!-- Sidebar Overlay (mobile only) -->
			<div class="sidebar-overlay" id="sidebarOverlay" onclick="closeUserSidebar()"></div>

			<!-- Main Content Wrapper -->
			<div class="main-wrapper" id="mainWrapper">

			<!-- User Sidebar -->
			<aside class="user-sidebar" id="userSidebar">
				<div class="sidebar-header">
					<div class="logo-icon">
						<i class="fas fa-mobile-alt"></i>
					</div>
					<div class="logo-content">
						<div class="logo-text">
							<span class="khmer">KHMER</span><span class="smm"> SMM</span>
						</div>
						<div class="logo-subtitle">Social Media Marketing in Cambodia</div>
					</div>
				</div>
				<div class="sidebar-content">
					<!-- User Card -->
					<div class="user-card">
						<div class="new-badge">NEW</div>
						<div class="user-avatar-large">
							<img src="https://storage.perfectcdn.com/n675sa/ugh7qnr2n0u93uky.png" alt="User Profile">
						</div>
						<div class="balance">Your Balance: {{ user.balance_formatted }}</div>
						<div class="username">User: {{ user.username }}</div>
					</div>

					<!-- Regular Menu -->
					<ul class="sidebar-menu">
						<li>
							<a href="{{ page_url('neworder') }}" class="{% if page.name == 'neworder' %}active{% endif %}" data-page="new-order">
								<i class="fas fa-shopping-cart"></i>
								New order
							</a>
						</li>
						<li>
							<a href="{{ page_url('orders') }}" class="{% if page.name == 'orders' %}active{% endif %}" data-page="my-orders">
								<i class="fas fa-list-alt"></i>
								My Orders
							</a>
						</li>
						<li>
							<a href="{{ page_url('addfunds') }}" class="{% if page.name == 'addfunds' %}active{% endif %}" data-page="add-funds">
								<i class="fas fa-wallet"></i>
								Add funds
							</a>
						</li>
						<li>
							<a href="{{ page_url('services') }}" class="{% if page.name == 'services' %}active{% endif %}" data-page="services">
								<i class="fas fa-cogs"></i>
								Services
							</a>
						</li>
						<li>
							<a href="{{ page_url('tickets') }}" class="{% if page.name == 'tickets' %}active{% endif %}" data-page="tickets">
								<i class="fas fa-ticket-alt"></i>
								Tickets
								{% if user.tickets_count > 0 %}
									<span class="badge">{{ user.tickets_count }}</span>
								{% endif %}
							</a>
						</li>
					</ul>

					<!-- Show More Button -->
					<a href="#" class="show-more-btn" id="showMoreBtn" onclick="toggleMoreMenu()">
						<span>Show more</span>
						<i class="fas fa-chevron-down"></i>
					</a>

					<!-- Additional Menu Items -->
					<div class="additional-menu" id="additionalMenu">
						<ul class="sidebar-menu">
							<li>
								<a href="{{ page_url('dashboard') }}" class="{% if page.name == 'dashboard' %}active{% endif %}">
									<i class="fas fa-th-large"></i>
									Dashboard
								</a>
							</li>
							<li>
								<a href="{{ page_url('api') }}" class="{% if page.name == 'api' %}active{% endif %}">
									<i class="fas fa-code"></i>
									API
								</a>
							</li>
							<li>
								<a href="{{ page_url('affiliates') }}" class="{% if page.name == 'affiliates' %}active{% endif %}">
									<i class="fas fa-external-link-alt"></i>
									Affiliates
								</a>
							</li>
							<li>
								<a href="{{ page_url('child') }}" class="{% if page.name == 'child' %}active{% endif %}">
									<i class="fas fa-user-friends"></i>
									Child panel
								</a>
							</li>
							<li>
								<a href="{{ page_url('faq') }}" class="{% if page.name == 'faq' %}active{% endif %}">
									<i class="fas fa-question-circle"></i>
									faq
								</a>
							</li>
							<li>
								<a href="{{ page_url('updates') }}" class="{% if page.name == 'updates' %}active{% endif %}">
									<i class="fas fa-bell"></i>
									Updates
								</a>
							</li>
							<li>
								<a href="{{ page_url('terms') }}" class="{% if page.name == 'terms' %}active{% endif %}">
									<i class="fas fa-file-contract"></i>
									Terms
								</a>
							</li>
						</ul>
					</div>
				</div>
			</aside>

			<!-- Original Navbar for Authenticated Users -->
			<nav class="navbar navbar-default navbar-static-top {% if site['rtl'] %} rtl-navbar {% endif %}">
				<div class="container-fluid">
					<div class="navbar-header">
						<!-- Sidebar Toggle Button in Header -->
						<button class="sidebar-toggle-btn" onclick="toggleUserSidebar()">
							<i class="fas fa-bars"></i>
						</button>
						<button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar" aria-expanded="false" aria-controls="navbar">
							<span class="sr-only">Toggle navigation</span>
							<span class="icon-bar"></span>
							<span class="icon-bar"></span>
							<span class="icon-bar"></span>
						</button>
						<a class="navbar-brand" href="{{ page_url('index') }}">
							{% if site['logo'] %}
								<img src="{{ site['logo'] }}" alt="{{ site['name'] }}" title="{{ site['name'] }}">
							{% else %}
								{{ site['name'] }}
							{% endif %}
						</a>
					</div>
					<div id="navbar" class="collapse navbar-collapse">
						<ul class="nav navbar-nav navbar-left-block">
							{% for menu in site['menu'] %}
								<li {% if menu['active'] %} class="active" {% endif %}>
									<a href="{{ menu['link'] }}" {% if menu['external'] %} target="_blank" {% endif %}>
										{% if menu['icon'] %}
											<i class="navbar-icon {{ menu['icon'] }}"></i>
										{% endif %}
										{{ menu['name'] }}
									</a>
								</li>
							{% endfor %}
						</ul>
						<ul class="nav navbar-nav navbar-right navbar-right-block">
							{% for menu in site['account_menu'] %}
								{% if not menu['link'] %}
									{% if site.currencies %}
										<li class="dropdown dropdown-currencies">
											<a class="dropdown-toggle" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <span class="badge">{{ user.balance_formatted }}</span>
                        <span class="caret"></span>
                      </a>
											<ul class="dropdown-menu" id="currencies-list">
												{% for key, rate in site.currencies %}
													<li>
														<a href="#" id="currencies-item" data-rate-key="{{ key }}" data-rate-symbol="{{ rate.symbol }}">{{ key }} {{ rate.symbol }}</a>
													</li>
												{% endfor %}
											</ul>
										</li>
									{% else %}
										<li>
											<a>{{ menu['name'] }}</a>
										</li>
									{% endif %}
								{% else %}
									<li {% if menu['active'] %} class="active" {% endif %}>
										<a href="{{ menu['link'] }}" {% if menu['external'] %} target="_blank" {% endif %}>{{ menu['name'] }}</a>
									</li>
								{% endif %}
							{% endfor %}
						</ul>
					</div>
				</div>
			</nav>

			<!-- Custom Header for Non-Logged-In Users -->
			<nav class="khmer-header">
				<div class="header-content">
						<!-- Logo Section -->
						<div class="header-logo">
							<a href="{{ page_url('index') }}" class="logo-link">
								<div class="logo-container">
									<i class="fas fa-mobile-alt logo-icon"></i>
									<span class="logo-text">
										<span class="chhean">CHHEAN</span><span class="smm">SMM</span>
									</span>
								</div>
							</a>
						</div>

						<!-- Navigation Menu -->
						<div class="header-nav">
							<ul class="nav-menu">
								<li><a href="/signin">Sign in</a></li>
								<li><a href="/services">Services</a></li>
								<li><a href="/faq">Free Tools</a></li>
								<li><a href="/api">API</a></li>
								<li><a href="/blog">Blogs</a></li>
								<li><a href="/about">About Us</a></li>
							</ul>
						</div>

						<!-- Signup Button -->
						<div class="header-signup">
							<a href="/signup" class="signup-btn">
								<i class="fas fa-user-plus"></i>
								Signup
							</a>
						</div>

						<!-- Mobile Menu Toggle -->
						<div class="mobile-menu-toggle">
							<span></span>
							<span></span>
							<span></span>
						</div>
					</div>
			</nav>


		<footer class="footer-section">
			<div class="container">
				<div class="row">
					<!-- Logo and Description -->
					<div class="col-lg-4 col-md-6 mb-4">
						<div class="footer-brand">
							<div class="footer-logo">
								<div class="logo-container">
									<i class="fas fa-mobile-alt logo-icon"></i>
									<span class="logo-text">
										<span class="chhean">CHHEAN</span><span class="smm">SMM</span>
									</span>
								</div>
							</div>
							<p class="footer-description">
								Explore the world of social media marketing with Chhean SMM, Cambodia's leading provider of affordable and effective SMM panels. Elevate your online presence and grow your brand with our expert services designed just for you.
							</p>
							<div class="social-media-icons">
								<a href="#" class="social-icon facebook"><i class="fab fa-facebook-f"></i></a>
								<a href="#" class="social-icon youtube"><i class="fab fa-youtube"></i></a>
								<a href="#" class="social-icon twitter"><i class="fab fa-twitter"></i></a>
								<a href="https://t.me/chheansmm_support" target="_blank" class="social-icon telegram"><i class="fab fa-telegram-plane"></i></a>
								<a href="#" class="social-icon instagram"><i class="fab fa-instagram"></i></a>
								<a href="#" class="social-icon skype"><i class="fab fa-skype"></i></a>
							</div>
						</div>
					</div>

					<!-- Quick Links -->
					<div class="col-lg-2 col-md-6 mb-4">
						<div class="footer-links">
							<h5 class="footer-title">Quick Links</h5>
							<ul class="footer-menu">
								<li><a href="/">Home</a></li>
								<li><a href="/about">About Us</a></li>
								<li><a href="/contact">Contact Us</a></li>
								<li><a href="/blog">Blog</a></li>
							</ul>
						</div>
					</div>

					<!-- Other Links -->
					<div class="col-lg-2 col-md-6 mb-4">
						<div class="footer-links">
							<h5 class="footer-title">Others Links</h5>
							<ul class="footer-menu">
								<li><a href="/terms">Terms Of Services</a></li>
								<li><a href="/privacy">Privacy Policy</a></li>
								<li><a href="/refund">Refund Policy</a></li>
							</ul>
						</div>
					</div>

					<!-- Contact Info -->
					<div class="col-lg-4 col-md-6 mb-4">
						<div class="footer-contact">
							<div class="contact-item">
								<div class="contact-icon location">
									<i class="fas fa-map-marker-alt"></i>
								</div>
								<div class="contact-info">
									<p>Toul Tompoung, Sangkat<br>Toul Tompoung 1, Khan<br>Chamkarmon, Phnom<br>Penh, Cambodia</p>
								</div>
							</div>
							<div class="contact-item">
								<div class="contact-icon email">
									<i class="fas fa-envelope"></i>
								</div>
								<div class="contact-info">
									<p><a href="https://t.me/chheansmm_support" target="_blank" class="telegram-link">@chheansmm_support</a> (For inquiries or support)</p>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- Footer Bottom -->
				<div class="footer-bottom">
					<div class="row">
						<div class="col-12">
							<div class="footer-copyright">
								<p>&copy; 2022 - 2025 All Right Reserved | Chhean SMM Limited</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</footer>

		<!-- Notifications wrapper -->
		<div id="notify-wrapper" class="alert alert-success hidden" style="display: none;"></div>

		{{ site['custom_footer'] }}

		<!-- CRITICAL: Prevent duplicate dropdown initialization BEFORE site scripts load -->
		<script>
		// Override Select2 to prevent duplicate initialization
		if (typeof $ !== 'undefined') {
			const originalSelect2 = $.fn.select2;
			$.fn.select2 = function(options) {
				return this.each(function() {
					if (!$(this).hasClass('select2-initialized')) {
						$(this).addClass('select2-initialized');
						return originalSelect2.call($(this), options);
					} else {
						console.log('🚫 Prevented duplicate Select2 initialization on:', this);
						return $(this);
					}
				});
			};
			console.log('✅ Select2 protection activated');
		}
		</script>

		{% for script in site['scripts'] %}
			<script type="text/javascript" {% if script['src'] %} src="{{ script['src'] }}" {% endif %}>
				{% if script['code'] %}
{{ script['code'] }}
{% endif %}
			</script>
		{% endfor %}

		<!-- Layout.twig JavaScript - ONLY for sidebar and profile, NOT for forms -->
		<script>
		// ONLY Sidebar and Profile functionality - NO form handling
		function toggleUserSidebar() {
			const sidebar = document.getElementById('userSidebar');
			const overlay = document.getElementById('sidebarOverlay');
			const mainWrapper = document.getElementById('mainWrapper');
			const body = document.body;

			if (!sidebar || !overlay || !mainWrapper) {
				return;
			}

			const isCurrentlyOpen = sidebar.classList.contains('show');

			if (isCurrentlyOpen) {
				sidebar.classList.remove('show');
				mainWrapper.classList.remove('sidebar-open');
				body.classList.remove('sidebar-open');
				overlay.classList.remove('show');
			} else {
				sidebar.classList.add('show');
				mainWrapper.classList.add('sidebar-open');
				body.classList.add('sidebar-open');

				if (window.innerWidth <= 768) {
					overlay.classList.add('show');
				}
			}

			localStorage.setItem('userSidebarOpen', sidebar.classList.contains('show'));
		}

		function closeUserSidebar() {
			const sidebar = document.getElementById('userSidebar');
			const overlay = document.getElementById('sidebarOverlay');
			const mainWrapper = document.getElementById('mainWrapper');
			const body = document.body;

			if (sidebar) sidebar.classList.remove('show');
			if (overlay) overlay.classList.remove('show');
			if (mainWrapper) mainWrapper.classList.remove('sidebar-open');
			if (body) body.classList.remove('sidebar-open');

			localStorage.setItem('userSidebarOpen', false);
		}

		function toggleSidebar() {
			toggleUserSidebar();
		}

		function toggleProfileDropdown() {
			const dropdown = document.getElementById('profileDropdown');
			if (dropdown) {
				dropdown.classList.toggle('show');
			}
		}

		// Make functions globally accessible
		window.toggleProfileDropdown = toggleProfileDropdown;
		window.toggleSidebar = toggleSidebar;
		window.toggleUserSidebar = toggleUserSidebar;
		window.closeUserSidebar = closeUserSidebar;

		// Simple initialization - NO form interference
		document.addEventListener('DOMContentLoaded', function() {
			// Restore sidebar state
			if (document.getElementById('userSidebar')) {
				const isOpen = localStorage.getItem('userSidebarOpen') === 'true';
				if (isOpen) {
					const sidebar = document.getElementById('userSidebar');
					const mainWrapper = document.getElementById('mainWrapper');
					const overlay = document.getElementById('sidebarOverlay');

					if (sidebar) sidebar.classList.add('show');
					if (mainWrapper) mainWrapper.classList.add('sidebar-open');
					if (document.body) document.body.classList.add('sidebar-open');

					if (window.innerWidth <= 768 && overlay) {
						overlay.classList.add('show');
					}
				}
			}

			// Dropdown protection is now handled BEFORE site scripts load
		});

		// ONLY handle profile dropdown - NO form elements
		document.addEventListener('click', function(event) {
			// Profile dropdown only
			const dropdown = document.getElementById('profileDropdown');
			const profileBtn = document.querySelector('.profile-btn');

			if (dropdown && profileBtn && !dropdown.contains(event.target) && !profileBtn.contains(event.target)) {
				dropdown.classList.remove('show');
			}

			// Sidebar overlay only (mobile)
			if (window.innerWidth <= 768) {
				const overlay = document.getElementById('sidebarOverlay');
				if (overlay && overlay.contains(event.target) && event.target === overlay) {
					closeUserSidebar();
				}
			}
		});
		</script>

	</body>
</html>
