<div class="container-fluid">
  <div class="row">
    <div class="col-lg-12">
      <ul class="nav nav-pills {% if site['rtl'] %} rtl-nav {% endif %}">
        <li {% if 'all' == status %}class="active"{% endif %}><a href="{{ page['url'] }}">{{ lang('dripfeed.status.all') }}</a></li>
        <li {% if 'active' == status %}class="active"{% endif %}><a href="{{ page['url'] }}/active">{{ lang('dripfeed.status.active') }}</a></li>
        <li {% if 'completed' == status %}class="active"{% endif %}><a href="{{ page['url'] }}/completed">{{ lang('dripfeed.status.completed') }}</a></li>
        <li {% if 'canceled' == status %}class="active"{% endif %}><a href="{{ page['url'] }}/canceled">{{ lang('dripfeed.status.canceled') }}</a></li>
        <li class="pull-right search">
          <form action="{{ page['url'] }}" method="get" id="history-search">
            <div class="input-group">
              <input type="text" name="search" class="form-control" placeholder="{{lang('general.search.placeholder')}}" value="{{search}}">
              <span class="input-group-btn">
                <button type="submit" class="btn btn-default"><i class="fa fa-search" aria-hidden="true"></i></button>
              </span>
            </div>
          </form>
        </li>
      </ul>
      <div class="well well-float">
        <table class="table {% if site['rtl'] %} rtl-table {% endif %}">
          <thead>
          <tr>
            <th>{{ lang('dripfeed.id') }}</th>
            <th>{{ lang('dripfeed.date') }}</th>
            <th>{{ lang('dripfeed.link') }}</th>
            <th>{{ lang('dripfeed.total_charge') }}</th>
            <th>{{ lang('dripfeed.quantity') }}</th>
            <th>{{ lang('dripfeed.service') }}</th>
            <th>{{ lang('dripfeed.runs') }}</th>
            <th>{{ lang('dripfeed.interval') }}</th>
            <th>{{ lang('dripfeed.total_quantity') }}</th>
            <th>{{ lang('dripfeed.status') }}</th>
            <th></th>
          </tr>
          </thead>
          <tbody>

          {% for dripFeed in dripFeedList %}
            <tr>
              <td>{{ dripFeed['id'] }}</td>
              <td>{{ dripFeed['date'] }}</td>
              <td class="link">{{ dripFeed['link'] }}</td>
              <td nowrap="">{{ dripFeed['total_charges'] }}</td>
              <td>{{ dripFeed['quantity'] }}</td>
              <td>{{ dripFeed['service'] }}</td>
              <td class="nowrap">
                {% if dripFeed['runs_current'] %}
                  <a href="{{ page_url('orders') }}?drip-feed={{ dripFeed['id'] }}">
                      {{ dripFeed['runs_current'] }}
                  </a>
                {% else %}
                  {{ dripFeed['runs_current'] }}
                {% endif %} / {{ dripFeed['runs_all'] }}
              </td>
              <td>{{ dripFeed['interval'] }}</td>
              <td>{{ dripFeed['total_quantity'] }}</td>
              <td>{{ dripFeed['status_name'] }}</td>
            </tr>
          {% endfor %}

          </tbody>
        </table>
      </div>

      {% if pagination['count'] > 100 %}
        <ul class="pagination {% if site['rtl'] %} rtl-pagination {% endif %}">
          {% if pagination['current'] != 1 %}
            <li>
              <a href="{{ page['url'] }}/{{ status }}/{{ pagination['last'] }}{% if search %}?search={{ search }}{% endif %}" aria-label="Previous">
                <span aria-hidden="true">&laquo;</span>
              </a>
            </li>
          {% endif %}

          {% set r, l = 3, 3 %}

          {% if pagination['current'] == 1 %}
            {% set r = 6 %}
          {% endif %}

          {% if pagination['current'] == 2 %}
            {% set r = 5 %}
          {% endif %}

          {% if pagination['current'] >= pagination['pages'] %}
            {% set l = 5 %}
          {% endif %}

          {% for i in 1..ceil(pagination['pages']) %}
            {% if i >= (pagination['current']-l) and i <= (pagination['current']+r) %}
              <li{% if i == pagination['current'] %} class="active"{% endif %}><a href="{{ page['url'] }}/{{ status }}/{{i}}{% if search %}?search={{ search }}{% endif %}">{{i}}</a></li>
            {% endif %}
          {% endfor %}

          {% if pagination['current'] < pagination['pages'] %}
            <li>
              <a href="{{ page['url'] }}/{{ status }}/{{ pagination['next'] }}{% if search %}?search={{ search }}{% endif %}" aria-label="Next">
                <span aria-hidden="true">&raquo;</span>
              </a>
            </li>
          {%endif%}
        </ul>
      {% endif %}

    </div>
  </div>
</div>

