<div class="container-fluid">
  <div class="row">
    <div class="col-lg-12">
      <div class="row">
        <div class="col-lg-12">
          <ul class="nav nav-pills pull-right {% if site['rtl'] %} rtl-nav {% endif %}">
            {% if types %}
              <li>
                <div class="dropdown">
                  <button class="btn btn-primary dropdown-toggle" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <span>{{ type.label }}</span>
                    <span class="caret"></span>
                  </button>
                  <ul class="dropdown-menu">
                    {% for typeItem in types %}
                      <li class="{% if typeItem.key == type.key %} active{% endif %}">
                        <a class="dropdown-item" href="{{ page['url'] }}{% if typeItem.key != 'all' %}{{ '/' ~ typeItem.key }}{% endif %}">
                          {{ typeItem.label }}
                        </a>
                      </li>
                    {% endfor %}
                  </ul>
                </div>
              </li>
            {% endif %}
            <li class="search">
              <form action="{{ page['url'] }}" method="get" id="history-search">
                <div class="input-group">
                  <input type="text" name="search" class="form-control" value="{{ search }}"
                        placeholder="{{ lang('general.search.placeholder') }}">
                  <span class="input-group-btn">
                    <button type="submit" class="btn btn-default"><i class="fa fa-search" aria-hidden="true"></i></button>
                  </span>
                </div>
              </form>
            </li>
          </ul>  
        </div>
      </div>
      <div class="well well-float">
        <table class="table {% if site['rtl'] %} rtl-table {% endif %}">
          <thead>
          <tr>
            <th>{{ lang('updates.service') }}</th>
            <th>{{ lang('updates.date') }}</th>
            <th>{{ lang('updates.update') }}</th>
          </tr>
          </thead>
          <tbody>
          {% for update in updatesList %}
            <tr>
              <td class="service-name">{{ update['service_id'] }} - {{ update['service'] }}</td>
              <td>{{ update['date'] }}</td>
              <td>{{ update['update'] }}</td>
            </tr>
          {% endfor %}
          </tbody>
        </table>
      </div>

      {% if pagination['count'] > 100 %}
        {% if search %}
          {% set params = {} %}
            {% set params = params | merge(['search=' ~ search]) %}
          {% set params = '?' ~ params|join('&') %}
        {% endif %}
        <ul class="pagination {% if site['rtl'] %} rtl-pagination {% endif %}">
          {% if pagination['current'] != 1 %}
            <li>
            <a href="{{ page['url'] }}{{ type.key != 'all' ? '/' ~ type.key : '' }}{{ pagination['prev'] > 1 ? '/' ~ pagination['prev'] : '' }}{{ params }}" aria-label="Previous">
              <span aria-hidden="true">&laquo;</span>
            </a>
            </li>
          {% endif %}

          {% set r, l = 3, 3 %}

          {% if pagination['current'] == 1 %}
            {% set r = 6 %}
          {% endif %}

          {% if pagination['current'] == 2 %}
            {% set r = 5 %}
          {% endif %}

          {% if pagination['current'] >= pagination['pages'] %}
            {% set l = 5 %}
          {% endif %}

          {% for i in 1..ceil(pagination['pages']) %}
            {% if i >= (pagination['current']-l) and i <= (pagination['current']+r) %}
              <li{% if i == pagination['current'] %} class="active"{% endif %}><a
                    href="{{ page['url'] }}{{ type.key != 'all' ? '/' ~ type.key : '' }}{{ i > 1 ? '/' ~ i : '' }}{{ params }}">{{ i }}</a></li>
            {% endif %}
          {% endfor %}

          {% if pagination['current'] < pagination['pages'] %}
            <li>
              <a href="{{ page['url'] }}{{ type.key != 'all' ? '/' ~ type.key : '' }}/{{ pagination['next'] }}{{ params }}" aria-label="Next">
                <span aria-hidden="true">&raquo;</span>
              </a>
            </li>
          {% endif %}
        </ul>
      {% endif %}
    </div>
  </div>
</div>
