<!DOCTYPE html>
<html lang="en" {% if site['rtl'] %}dir="rtl" class="rtl"{% endif %}>
	<head>
		<!-- Critical style block to prevent dark mode flash -->
		<style id="critical-theme">
			/* This style executes immediately before any rendering */
			html.dark-theme, 
			html.dark-theme body,
			html.dark-theme .sidebar,
			html.dark-theme .content-area,
			html.dark-theme .auth-header,
			html.dark-theme .card,
			html.dark-theme .table-container,
			html.dark-theme .orders-container,
			html.dark-theme .info-bar,
			html.dark-theme .main-content,
			html.dark-theme #mainContent,
			html.dark-theme .neworder-container {
				background-color: #111827 !important;
				color: #e0e0e0 !important;
			}
			
			html.dark-theme .sidebar {
				background: linear-gradient(180deg, #111827 0%, #1a2035 100%) !important;
			}
			
			html.dark-theme .auth-header {
				background-color: #16213e !important;
			}
			
			/* Add a deliberate delay to body opacity to ensure styles are loaded */
			body {
				transition: none !important;
			}
			
			html.theme-transitioning * {
				transition: none !important;
			}
		</style>
		
		<script>
			// IMMEDIATE theme application before any rendering
			(function() {
				try {
					var theme = localStorage.getItem('theme');
					if (theme === 'dark-mode') {
						document.documentElement.classList.add('dark-theme');
						document.documentElement.classList.add('theme-transitioning');
						// Crucial: apply class to body immediately
						document.addEventListener('DOMContentLoaded', function() {
							document.body.classList.add('dark-mode');
							// Wait a moment before removing transition blocking
							setTimeout(function() {
								document.documentElement.classList.remove('theme-transitioning');
							}, 50);
						});
					}
				} catch(e) {
					console.error('Theme init error:', e);
				}
			})();
		</script>
		
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, initial-scale=1">
		<link rel="icon" type="image/png" href="{{ site['favicon'] }}">
		<title>{{ site['title'] }}</title>
		
		<!-- Prevent flash of unstyled content by applying theme immediately -->
		<script>
			// Immediately apply stored theme preference
			(function() {
				var storedTheme = localStorage.getItem('theme');
				
				// Default to light mode if no preference is stored
				if (!storedTheme) {
					storedTheme = 'light-mode';
					localStorage.setItem('theme', storedTheme);
				}
				
				// Apply the theme immediately
				document.documentElement.classList.add('theme-transitioning');
				if (storedTheme === 'dark-mode') {
					document.documentElement.classList.add('dark-theme');
					document.body.classList.add('dark-mode');
				}
				
				// Remove transition class after a tiny delay
				setTimeout(function() {
					document.documentElement.classList.remove('theme-transitioning');
				}, 1);
			})();
		</script>
		
		<!-- CSS Theme Transition Styles -->
		<style>
			:root {
				--theme-transition-time: 0.3s;
			}
			
			/* Prevent transition on initial load */
			html.theme-transitioning * {
				transition: none !important;
			}
			
			/* Fix theme toggle button visibility in both themes */
			.theme-toggle-btn {
				background-color: rgba(255, 255, 255, 0.1);
				color: #555;
				border-radius: 50%;
				width: 40px;
				height: 40px;
				display: flex;
				align-items: center;
				justify-content: center;
				transition: all 0.3s ease;
				border: 1px solid rgba(0, 0, 0, 0.1);
				cursor: pointer;
			}
			
			.theme-toggle-btn i {
				font-size: 18px;
				display: flex;
				align-items: center;
				justify-content: center;
			}
			
			.theme-toggle-btn:hover {
				background-color: rgba(0, 0, 0, 0.05);
				color: #333;
			}
			
			body.dark-mode .theme-toggle-btn {
				background-color: rgba(255, 255, 255, 0.1);
				color: #e0e0e0;
				border: 1px solid rgba(255, 255, 255, 0.15);
			}
			
			body.dark-mode .theme-toggle-btn:hover {
				background-color: rgba(77, 163, 255, 0.2);
				color: #4da3ff;
			}
			
			/* Make sun icon hidden by default, shown in dark mode */
			.fa-sun {
				display: none;
			}
			
			.fa-moon {
				display: flex;
			}
			
			body.dark-mode .fa-sun {
				display: flex;
			}
			
			body.dark-mode .fa-moon {
				display: none;
			}
			
			/* Add smooth transitions for theme changes */
			body, .sidebar, .content-area, .auth-header, table, input, select, button, a, div,
			.table-container, .orders-table, .form-control, textarea, .modal-content, 
			.info-bar, .filter-btn, .status-badge, .filter-buttons, .dark-toggle, .btn,
			.header-container, .pagination, .auth-icon-link, .auth-dropdown, .card, .neworder-container {
				transition: background-color var(--theme-transition-time) ease,
							color var(--theme-transition-time) ease,
							border-color var(--theme-transition-time) ease,
							box-shadow var(--theme-transition-time) ease;
			}
		</style>
		
		<meta name="keywords" content="{{ site['seo_key'] }}">
		<meta name="description" content="{{ site['seo_desc'] }}">
		{% if site['favicon'] %}
			<link rel="shortcut icon" type="image/ico" href="{{ site['favicon'] }}"/>
		{% endif %}

		<!-- Font Awesome -->
		<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" integrity="sha512-SfTiTlX6kk+qitfevl/7LibUOeJWlt9rbyDn92a1DqWOw9vWG2MFoays0sgObmWazO5BQPiFucnnEAjpAB+/Sw==" crossorigin="anonymous" referrerpolicy="no-referrer" />

		<!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
		<!--[if lt IE 9]>
		  <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
		  <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
		  <![endif]-->
		{{ site['custom_header'] }}

		{% for style in site['styles'] %}
			<link rel="stylesheet" type="text/css" href="{{ style['href'] }}">
		{% endfor %}
		<script>
			window.modules = {};
		</script>
		<style>
			/* Global Styles */
			body {
				font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;
				line-height: 1.6;
				color: #444;
				margin: 0;
				padding: 0;
				background-color: #f8f9fa;
			}
			
			.container {
				max-width: 1200px;
				margin: 0 auto;
				padding: 0 15px;
			}
			
			/* Sidebar Styles - Enhanced for both desktop and mobile */
			.sidebar {
				position: fixed;
				top: 0;
				left: 0;
				width: 280px;
				height: 100%;
				background: linear-gradient(180deg, #1e88e5 0%, #1565c0 100%);
				color: #fff;
				overflow-y: auto;
				z-index: 1050;
				transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
				box-shadow: 0 0 20px rgba(0,0,0,0.2);
			}
			
			.sidebar.collapsed {
				transform: translateX(-100%);
			}
			
			/* Auth Header Styles - UPDATED */
			.auth-header {
				position: fixed;
				top: 0;
				left: 280px;
				right: 0;
				height: 70px;
				padding: 0 25px;
				background-color: #ffffff;
				display: flex;
				align-items: center;
				justify-content: space-between;
				z-index: 999;
				box-shadow: 0 2px 5px rgba(0,0,0,0.08);
				transition: all 0.3s ease;
			}
			
			.sidebar.collapsed ~ .auth-header {
				left: 0;
			}
			
			.auth-logo img {
				height: 40px;
			}
			
			.auth-actions {
				display: flex;
				align-items: center;
				gap: 20px;
			}
			
			.auth-icon-link {
				color: #555;
				font-size: 18px;
				display: flex;
				align-items: center;
				justify-content: center;
				transition: color 0.2s;
				padding: 10px;
				border-radius: 50%;
				width: 42px;
				height: 42px;
				text-decoration: none;
				background-color: #f0f2f5;
			}
			
			.auth-icon-link:hover {
				color: #1e88e5;
				background-color: #e4e6eb;
			}
			
			.auth-icon-link i {
				font-size: 18px;
			}
			
			/* Dropdown Styles */
			.auth-dropdown {
				position: relative;
			}
			
			.dropdown-content {
				position: absolute;
				top: 52px;
				right: 0;
				background-color: white;
				border-radius: 12px;
				box-shadow: 0 5px 20px rgba(0,0,0,0.15);
				min-width: 240px;
				z-index: 1000;
				display: none;
				padding: 0;
				overflow: hidden;
				border: 1px solid rgba(0,0,0,0.05);
			}
			
			.auth-dropdown:hover .dropdown-content {
				display: none; /* Prevent hover from showing dropdown, we use JS instead */
			}
			
			.dropdown-content a {
				display: flex;
				align-items: center;
				padding: 15px;
				color: #333;
				text-decoration: none;
				font-size: 14px;
				transition: background-color 0.2s;
			}
			
			.dropdown-content a:hover {
				background-color: #f5f8ff;
			}
			
			.dropdown-content a i {
				margin-right: 12px;
				color: #1e88e5;
				width: 20px;
				text-align: center;
			}
			
			/* Toggle Sidebar Button */
			.toggle-sidebar {
				position: fixed;
				left: 280px;
				top: 15px;
				width: 42px;
				height: 42px;
				background-color: #1e88e5;
				color: white;
				border: none;
				border-radius: 8px;
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;
				z-index: 1300;
				transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
				box-shadow: 0 2px 10px rgba(0,0,0,0.2);
			}
			
			.sidebar.collapsed ~ .toggle-sidebar,
			.sidebar.collapsed + .toggle-sidebar {
				left: 15px;
			}
			
			.toggle-sidebar:hover {
				background-color: #1565c0;
				transform: scale(1.05);
			}
			
			.toggle-sidebar i {
				font-size: 20px;
				transition: transform 0.3s;
			}
			
			body.sidebar-open .toggle-sidebar i {
				transform: rotate(180deg);
			}
			
			/* Main Content - UPDATED */
			.main-content {
				margin-left: 280px;
				padding: 90px 25px 25px;
				transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
			}
			
			.sidebar.collapsed ~ .main-content {
				margin-left: 0;
			}
			
			/* Responsive Styles - Enhanced for mobile */
			@media (max-width: 768px) {
				.sidebar {
					transform: translateX(-100%);
					width: 250px;
					z-index: 1050;
					box-shadow: 0 0 20px rgba(0,0,0,0.3);
				}
				
				body.sidebar-open .sidebar {
					transform: translateX(0);
				}
				
				.sidebar-overlay {
					display: none;
					position: fixed;
					top: 0;
					left: 0;
					width: 100%;
				height: 100%;
					background-color: rgba(0,0,0,0.5);
					z-index: 1040;
					opacity: 0;
					transition: opacity 0.3s ease;
				}
				
				body.sidebar-open .sidebar-overlay {
					display: block;
					opacity: 1;
				}
				
				.auth-header {
					left: 0;
					width: 100%;
					padding: 0 15px;
				}
				
				.auth-logo {
					margin-left: 50px !important;
				}
				
				.toggle-sidebar {
					left: 15px;
					z-index: 1060;
				}
				
				.main-content {
					margin-left: 0;
					padding: 70px 15px 15px;
					width: 100%;
				}
			}
			
			/* Additional styles for even smaller screens */
			@media (max-width: 576px) {
				.auth-header {
					height: 50px;
				padding: 0 10px;
				}
				
				.auth-logo img {
					height: 25px;
				}
				
				.toggle-sidebar {
					width: 30px;
					height: 30px;
					top: 10px;
				}
				
				.auth-icon-link {
					width: 28px;
					height: 28px;
				font-size: 14px;
			}
			
				.main-content {
					padding: 60px 10px 10px;
				}
				
				/* Better styling for mobile menu items */
				.sidebar-menu .nav-link {
					padding: 10px 15px;
					font-size: 14px;
				}
				
				/* User dropdown optimizations */
				.user-dropdown-header {
					padding: 10px;
				}
				
				.user-dropdown-avatar {
					width: 35px;
					height: 35px;
				}
			}
			
			/* Custom Navbar for non-auth */
			.custom-navbar {
				background-color: #fff;
				box-shadow: 0 1px 3px rgba(0,0,0,0.1);
				padding: 15px 0;
			}
			
			.custom-container {
				display: flex;
				align-items: center;
				justify-content: space-between;
				max-width: 1200px;
				margin: 0 auto;
				padding: 0 15px;
			}
			
			.navbar-logo img {
				height: 40px;
			}
			
			.nav-buttons {
				display: flex;
				align-items: center;
				gap: 20px;
			}
			
			.nav-link {
				color: #444;
				text-decoration: none;
				font-size: 14px;
				transition: color 0.2s;
			}
			
			.nav-link:hover {
				color: #007bff;
			}
			
			.btn-signup {
				background-color: #007bff;
				color: #fff;
				padding: 8px 16px;
				border-radius: 4px;
				text-decoration: none;
				font-size: 14px;
				transition: background-color 0.2s;
			}
			
			.btn-signup:hover {
				background-color: #0069d9;
			}
			
			.mobile-toggle {
				display: none;
				background: none;
				border: none;
				cursor: pointer;
				padding: 0;
				width: 30px;
				height: 20px;
				position: relative;
			}
			
			.mobile-toggle span {
				display: block;
				width: 100%;
				height: 2px;
				background-color: #444;
				position: absolute;
				left: 0;
				transition: all 0.3s;
			}
			
			.mobile-toggle span:nth-child(1) {
				top: 0;
			}
			
			.mobile-toggle span:nth-child(2) {
				top: 50%;
				transform: translateY(-50%);
			}
			
			.mobile-toggle span:nth-child(3) {
				bottom: 0;
			}
			
			.mobile-toggle.open span:nth-child(1) {
				transform: rotate(45deg);
				top: 9px;
			}
			
			.mobile-toggle.open span:nth-child(2) {
				opacity: 0;
			}
			
			.mobile-toggle.open span:nth-child(3) {
				transform: rotate(-45deg);
				bottom: 9px;
			}
			
			.mobile-menu {
				display: none;
				position: fixed;
				top: 70px;
				left: 0;
				right: 0;
				background-color: #fff;
				padding: 15px;
				box-shadow: 0 2px 5px rgba(0,0,0,0.1);
				z-index: 999;
				flex-direction: column;
				gap: 15px;
			}
			
			.mobile-menu.open {
				display: flex;
			}
			
			@media (max-width: 992px) {
				.nav-buttons {
					display: none;
				}
				
				.mobile-toggle {
					display: block;
				}
			}

			/* Sidebar styles for authenticated users */
			/* 
			.sidebar {
				position: fixed;
				top: 0;
				left: 0;
				height: 100vh;
				background: #1e3a8a;
				width: 220px;
				transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
				z-index: 1001;
				overflow: hidden;
				box-shadow: 0 0 15px rgba(0,0,0,0.1);
			}
			
			.sidebar.collapsed {
				width: 0;
				overflow: hidden;
			}
			*/
			
			.sidebar-header {
				padding: 0;
				text-align: center;
				min-width: 280px;
				background: transparent;
			}
			
			.user-profile {
				background: rgba(255,255,255,0.08);
				padding: 30px 20px;
				margin: 0 0 20px 0;
				text-align: center;
				position: relative;
				box-shadow: 0 5px 15px rgba(0,0,0,0.1);
				border-bottom: 1px solid rgba(255,255,255,0.1);
			}
			
			.add-funds-button {
				position: absolute;
				right: 20px;
				top: 20px;
				width: 36px;
				height: 36px;
				background: rgba(255,255,255,0.2);
				border: none;
				border-radius: 50%;
				color: white;
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;
				transition: all 0.3s;
				font-size: 16px;
				box-shadow: 0 2px 10px rgba(0,0,0,0.2);
			}
			
			.add-funds-button:hover {
				background: #4caf50;
				transform: scale(1.1);
				box-shadow: 0 4px 15px rgba(0,0,0,0.3);
			}
			
			.user-avatar-wrapper {
				position: relative;
				width: 90px;
				height: 90px;
				margin: 0 auto 15px;
			}
			
			.user-avatar {
				width: 90px;
				height: 90px;
				border-radius: 50%;
				border: 3px solid rgba(255,255,255,0.2);
				object-fit: cover;
				box-shadow: 0 5px 15px rgba(0,0,0,0.2);
				transition: all 0.3s ease;
			}
			
			.user-avatar:hover {
				border-color: rgba(255,255,255,0.4);
				transform: scale(1.03);
			}
			
			.user-status {
				position: absolute;
				bottom: 3px;
				right: 3px;
				width: 20px;
				height: 20px;
				border-radius: 50%;
				background-color: #4caf50;
				border: 3px solid #121858;
				box-shadow: 0 2px 5px rgba(0,0,0,0.2);
			}
			
			.user-name {
				font-size: 18px;
				font-weight: 600;
				color: white;
				margin-bottom: 5px;
				transition: all 0.3s ease;
			}
			
			.user-name:hover {
				color: #b3e5fc;
			}
			
			.user-email {
				font-size: 13px;
				color: rgba(255,255,255,0.7);
				margin-bottom: 15px;
			}
			
			.user-balance {
				background: rgba(255,255,255,0.12);
				padding: 10px 15px;
				border-radius: 30px;
				display: inline-flex;
				align-items: center;
				gap: 10px;
				font-weight: 600;
				font-size: 16px;
				color: #e0f7fa;
				box-shadow: 0 3px 10px rgba(0,0,0,0.1);
				transition: all 0.3s ease;
			}
			
			.user-balance:hover {
				background: rgba(255,255,255,0.18);
				transform: translateY(-2px);
			}
			
			.user-balance i {
				font-size: 18px;
				color: #b3e5fc;
			}
			
			.user-balance .amount {
				color: #fff;
				font-weight: 700;
			}
			
			.sidebar-menu {
				padding: 0 15px 20px;
				background: transparent;
			}
			
			.nav-item {
				margin: 5px 0;
				background: transparent;
			}
			
			/* Sidebar Menu Items */
			.sidebar-menu .nav-link {
				display: flex;
				align-items: center;
				padding: 12px 20px;
				color: rgba(255,255,255,0.8);
				text-decoration: none;
				transition: all 0.3s;
				font-size: 15px;
				border-radius: 10px;
				border-left: none;
				margin: 2px 0;
			}
			
			.sidebar-menu .nav-link:hover {
				background: rgba(255,255,255,0.12);
				color: #fff;
				transform: translateX(5px);
			}
			
			.sidebar-menu .nav-link.active {
				background: rgba(255,255,255,0.18);
				color: #fff;
				box-shadow: 0 4px 10px rgba(0,0,0,0.1);
			}
			
			.sidebar-menu .nav-link i {
				width: 24px;
				text-align: center;
				margin-right: 15px;
				font-size: 18px;
				color: rgba(255,255,255,0.9);
			}
			
			/* Show More Button */
			.show-more-btn {
				display: flex;
				align-items: center;
				padding: 12px 20px;
				color: rgba(255,255,255,0.8);
				text-decoration: none;
				transition: all 0.3s;
				font-size: 15px;
				border-radius: 10px;
				cursor: pointer;
				background: transparent;
				width: 100%;
				border-left: none;
				margin-top: 5px;
			}
			
			.show-more-btn:hover {
				background: rgba(255,255,255,0.12);
				color: #fff;
				transform: translateX(5px);
			}
			
			/* More Menu Items */
			.more-menu {
				display: none;
				padding: 5px 0 0 0;
				margin: 0;
				list-style: none;
				background: transparent !important;
			}
			
			.more-menu .nav-item {
				background: transparent !important;
			}
			
			.more-menu a {
				background: transparent !important;
				display: flex;
				align-items: center;
				padding: 12px 20px 12px 30px;
				color: rgba(255,255,255,0.8);
				text-decoration: none;
				transition: all 0.3s;
				font-size: 14px;
				border-radius: 10px;
				margin: 2px 0;
			}
			
			.more-menu a:hover {
				background: rgba(255,255,255,0.12) !important;
				color: #fff;
				transform: translateX(5px);
			}
			
			.more-menu a i {
				width: 24px;
				text-align: center;
				margin-right: 15px;
				font-size: 16px;
				color: rgba(255,255,255,0.9);
			}
			
			/* Badge styles */
			.badge {
				padding: 4px 8px;
				border-radius: 10px;
				font-size: 11px;
				font-weight: 600;
				margin-left: auto;
			}
			
			.badge-orders {
				background-color: #00bcd4;
				color: white;
				box-shadow: 0 2px 5px rgba(0,188,212,0.3);
			}
			
			.badge-tickets {
				background-color: #ff9800;
				color: white;
				box-shadow: 0 2px 5px rgba(255,152,0,0.3);
			}
			
			.badge-premium {
				background: linear-gradient(45deg, #f9a825, #ffd600);
				color: #212121;
				padding: 3px 8px;
				border-radius: 20px;
				font-size: 10px;
				font-weight: 700;
				text-transform: uppercase;
				margin-left: 5px;
				box-shadow: 0 2px 5px rgba(255,214,0,0.3);
			}

			/* Auth Header Styles */
			.auth-header {
				position: fixed;
				top: 0;
				right: 0;
				left: 0;
				height: 70px;
				background-color: white;
				box-shadow: 0 2px 10px rgba(0,0,0,0.1);
				z-index: 1000;
				transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 0 25px;
				color: #333;
			}
			
			.auth-logo {
				margin-left: 280px;
				transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
			}
			
			.auth-header.sidebar-collapsed .auth-logo {
				margin-left: 60px;
			}
			
			/* User Dropdown Special Styling */
			.user-dropdown {
				min-width: 280px;
				padding: 0;
				border-radius: 12px;
				box-shadow: 0 5px 20px rgba(0,0,0,0.15);
				overflow: hidden;
				border: 1px solid rgba(0,0,0,0.05);
			}
			
			.user-dropdown-header {
				display: flex;
				align-items: center;
				padding: 20px;
				background: linear-gradient(135deg, #1a237e 0%, #121858 100%);
				border-radius: 12px 12px 0 0;
			}
			
			.user-dropdown-avatar {
				width: 50px;
				height: 50px;
				border-radius: 50%;
				margin-right: 15px;
				border: 2px solid rgba(255,255,255,0.3);
				object-fit: cover;
				box-shadow: 0 4px 10px rgba(0,0,0,0.2);
			}
			
			.user-dropdown-info {
				flex: 1;
			}
			
			.user-dropdown-name {
				color: white;
				font-weight: 600;
				font-size: 16px;
				margin-bottom: 5px;
			}
			
			.user-dropdown-balance {
				color: #e0f7fa;
				font-size: 14px;
				display: flex;
				align-items: center;
				gap: 8px;
				background: rgba(255,255,255,0.1);
				padding: 5px 10px;
				border-radius: 20px;
				width: fit-content;
			}
			
			.user-dropdown-balance i {
				font-size: 14px;
				color: #b3e5fc;
			}
			
			.dropdown-content a {
				padding: 15px;
				display: flex;
				align-items: center;
				color: #333;
				text-decoration: none;
				transition: background-color 0.2s;
			}
			
			.dropdown-content a:hover {
				background-color: #f5f8ff;
			}
			
			.dropdown-content a i {
				margin-right: 12px;
				width: 20px;
				text-align: center;
			}
			
			.dropdown-content a:last-child {
				border-top: 1px solid #eee;
			}

			/* Order Form and Search Results Styling */
			.order-container {
				background-color: #fff;
				border-radius: 8px;
				box-shadow: 0 2px 10px rgba(0,0,0,0.05);
				padding: 20px;
				margin-bottom: 20px;
			}
			
			/* Search Results and Service Display */
			.service-list {
				background-color: #fff;
				border-radius: 8px;
				box-shadow: 0 2px 10px rgba(0,0,0,0.05);
				margin-bottom: 20px;
				overflow: hidden;
			}
			
			.service-item {
				padding: 15px;
				border-bottom: 1px solid #eee;
				transition: background-color 0.2s;
			}
			
			.service-item:last-child {
				border-bottom: none;
			}
			
			.service-item:hover {
				background-color: #f8f9fa;
			}
			
			.service-item.selected {
				background-color: #e8f4ff;
			}
			
			/* TikTok and other service provider styling */
			.service-provider {
				font-weight: 600;
				color: #ff4d4f;
				margin-right: 5px;
			}
			
			.service-provider.tiktok {
				color: #ff0050;
			}
			
			.service-provider.instagram {
				color: #833AB4;
			}
			
			.service-provider.facebook {
				color: #1877F2;
			}
			
			.service-provider.youtube {
				color: #FF0000;
			}
			
			.service-description {
				margin: 5px 0;
				color: #555;
			}
			
			.service-price,
			[class*="per 1000"] {
				float: right;
				color: #28a745;
				font-weight: bold;
			}
			
			/* Search input styling */
			.search-container {
				position: relative;
				margin-bottom: 20px;
			}
			
			.search-input {
				width: 100%;
				padding: 12px 45px 12px 15px;
				border: 1px solid #ddd;
				border-radius: 4px;
				font-size: 14px;
				transition: all 0.3s;
			}
			
			.search-input:focus {
				border-color: #1e3a8a;
				box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
				outline: none;
			}
			
			.search-button {
				position: absolute;
				right: 5px;
				top: 50%;
				transform: translateY(-50%);
				background: none;
				border: none;
				color: #666;
				padding: 8px;
				cursor: pointer;
			}
			
			.search-button:hover {
				color: #1e3a8a;
			}

			/* Service selection indicators */
			.service-icon {
				display: inline-flex;
				align-items: center;
				justify-content: center;
				width: 20px;
				height: 20px;
				margin-right: 5px;
				border-radius: 50%;
				background-color: rgba(0,0,0,0.05);
				color: #666;
			}
			
			/* Service tag styling */
			.service-tag {
				display: inline-block;
				padding: 2px 6px;
				margin: 0 5px;
				border-radius: 3px;
				font-size: 10px;
				font-weight: 600;
				text-transform: uppercase;
				background-color: #f0f0f0;
				color: #666;
			}
			
			.service-tag.premium {
				background-color: #fff8e6;
				color: #d4a700;
			}
			
			.service-tag.fast {
				background-color: #e6f7ff;
				color: #0070f3;
			}
			
			.service-tag.instant {
				background-color: #e6fff0;
				color: #00b37e;
			}
			
			.service-tag.no-refill {
				background-color: #fff0f0;
				color: #ff4d4f;
			}
			
			/* Service selection */
			.category-container, 
			.service-container {
				background-color: #fff;
				border-radius: 8px;
				box-shadow: 0 2px 10px rgba(0,0,0,0.05);
				padding: 15px;
				margin-bottom: 15px;
			}
			
			.category-header,
			.service-header {
				font-weight: 600;
				color: #333;
				margin-bottom: 10px;
				display: flex;
				align-items: center;
			}
			
			.category-header i,
			.service-header i {
				margin-right: 8px;
				color: #1e3a8a;
			}
			
			/* Price display */
			.price-container {
				display: flex;
				align-items: center;
				margin: 5px 0;
			}
			
			.price-label {
				font-size: 12px;
				color: #666;
				margin-right: 5px;
			}
			
			.price-value {
				font-weight: 600;
				color: #28a745;
			}
			
			/* Platform buttons styling */
			.platform-buttons {
				display: flex;
				flex-wrap: wrap;
				gap: 10px;
				margin-bottom: 20px;
				padding: 15px;
				background-color: #fff;
				border-radius: 8px;
				box-shadow: 0 2px 10px rgba(0,0,0,0.05);
			}
			
			.platform-button {
				padding: 10px 15px;
				border-radius: 4px;
				background-color: #f8f9fa;
				border: 1px solid #dee2e6;
				color: #444;
				cursor: pointer;
				transition: all 0.2s;
				font-size: 14px;
				display: flex;
				align-items: center;
			}
			
			.platform-button:hover {
				background-color: #e9ecef;
				border-color: #ced4da;
			}
			
			.platform-button.active {
				background-color: #e8f4ff;
				border-color: #b8daff;
				color: #0d6efd;
			}
			
			.platform-button i {
				margin-right: 8px;
			}
			
			/* Form styling improvements */
			.form-container {
				background-color: #fff;
				border-radius: 8px;
				box-shadow: 0 2px 10px rgba(0,0,0,0.05);
				padding: 20px;
				margin-bottom: 20px;
			}
			
			.form-group {
				margin-bottom: 15px;
			}

			.form-label {
				display: block;
				margin-bottom: 8px;
				font-weight: 500;
				color: #333;
			}

			.form-control {
				width: 100%;
				padding: 10px 12px;
				border: 1px solid #ddd;
				border-radius: 4px;
				font-size: 14px;
				transition: all 0.3s;
			}

			.form-control:focus {
				border-color: #1e3a8a;
				box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
				outline: none;
			}
			
			/* Submit button styling */
			.btn-submit {
				display: inline-block;
				background-color: #1e3a8a;
				color: white;
				border: none;
				padding: 12px 20px;
				border-radius: 4px;
				font-size: 14px;
				font-weight: 500;
				cursor: pointer;
				transition: background-color 0.2s;
				width: 100%;
				text-align: center;
			}
			
			.btn-submit:hover {
				background-color: #0d47a1;
			}
			
			/* Video tutorial button */
			.btn-tutorial {
				display: inline-block;
				background-color: #f8f9fa;
				color: #444;
				border: 1px solid #dee2e6;
				padding: 12px 20px;
				border-radius: 4px;
				font-size: 14px;
				font-weight: 500;
				cursor: pointer;
				transition: all 0.2s;
				width: 100%;
				text-align: center;
			}
			
			.btn-tutorial:hover {
				background-color: #e9ecef;
				border-color: #ced4da;
			}
			
			.btn-tutorial i {
				margin-right: 8px;
			}

			/* Search Results Improvements */
			.search-results-container {
				background-color: #fff;
				border-radius: 8px;
				box-shadow: 0 2px 10px rgba(0,0,0,0.05);
				margin-bottom: 20px;
				overflow: hidden;
				border: 1px solid #e0e0e0;
				width: 100%;
				max-width: 100%;
			}
			
			/* Category heading */
			.search-category-heading {
				background-color: #f8f9fa;
				padding: 10px 15px;
				font-weight: 600;
				color: #1e3a8a;
				border-bottom: 1px solid #e0e0e0;
				margin: 0;
				font-size: 14px;
				display: flex;
				align-items: center;
			}
			
			/* Service items improvements */
			.search-service-item {
				padding: 15px;
				border-bottom: 1px solid #eee;
				background-color: #fff;
				position: relative;
				cursor: pointer;
				transition: background-color 0.2s;
				display: block; /* Forces block display to prevent click-through */
				z-index: 10; /* Ensure clickable */
			}
			
			.search-service-item:hover {
				background-color: #f5f8ff;
			}
			
			.search-service-item:last-child {
				border-bottom: none;
			}
			
			.search-service-item.selected {
				background-color: #e8f4ff;
			}
			
			/* Service provider name styling */
			.search-service-provider {
				font-weight: 700;
				margin-right: 5px;
				display: inline-block;
			}
			
			/* Facebook color */
			.search-service-provider.facebook {
				color: #1877F2;
			}
			
			/* Instagram color */
			.search-service-provider.instagram {
				color: #C13584;
			}
			
			/* TikTok color */
			.search-service-provider.tiktok {
				color: #ff0050;
			}
			
			/* YouTube color */
			.search-service-provider.youtube {
				color: #FF0000;
			}
			
			/* Twitter color */
			.search-service-provider.twitter {
				color: #1DA1F2;
			}
			
			/* Non-standard search results */
			.search-result-list {
				list-style: none;
				padding: 0;
				margin: 0;
				background-color: #fff;
				border-radius: 8px;
				box-shadow: 0 2px 10px rgba(0,0,0,0.05);
				border: 1px solid #e0e0e0;
				overflow: hidden;
				width: 100%;
			}
			
			.search-result-list li {
				padding: 15px;
				border-bottom: 1px solid #eee;
				background-color: #fff;
				position: relative;
				cursor: pointer;
				transition: background-color 0.2s;
				display: block; /* Forces block display */
				z-index: 10; /* Ensure clickable */
			}
			
			.search-result-list li:hover {
				background-color: #f5f8ff;
			}
			
			.search-result-list li.selected {
				background-color: #e8f4ff;
			}
			
			/* Service description */
			.search-service-desc {
				font-size: 14px;
				color: #333;
				margin: 2px 0;
			}
			
			/* Service features/tags */
			.search-service-feature {
				display: inline-block;
				padding: 2px 5px;
				margin: 0 3px;
				border-radius: 3px;
				font-size: 10px;
				font-weight: 600;
				text-transform: uppercase;
			}
			
			.search-service-feature.refill {
				background-color: #e6f7ff;
				color: #0070f3;
			}
			
			.search-service-feature.instant {
				background-color: #e6fff0;
				color: #00b37e;
			}
			
			.search-service-feature.emergency {
				background-color: #fff0f0;
				color: #ff4d4f;
			}
			
			.search-service-feature.fastest {
				background-color: #fff8e6;
				color: #d4a700;
			}
			
			/* Price styling */
			.search-service-price {
				font-weight: 600;
				color: #28a745;
				display: inline-block;
				margin-left: 5px;
			}
			
			/* Special keyword styling */
			.refill, .non-drop, .fastest, .instant {
				display: inline-block;
				padding: 2px 5px;
				margin: 0 3px;
				border-radius: 3px;
				font-size: 10px;
				font-weight: 600;
				text-transform: uppercase;
			}
			
			.refill {
				background-color: #e6f7ff;
				color: #0070f3;
			}
			
			.non-drop {
				background-color: #ede7f6;
				color: #5e35b1;
			}
			
			.fastest {
				background-color: #fff8e6;
				color: #d4a700;
			}
			
			.instant {
				background-color: #e6fff0;
				color: #00b37e;
			}
			
			/* Category label */
			.category-label {
				background-color: #f8f9fa;
				padding: 10px 15px;
				font-weight: 600;
				color: #1e3a8a;
				border-bottom: 1px solid #e0e0e0;
				margin: 10px 0 0 0;
				font-size: 14px;
				display: block;
				border-radius: 8px 8px 0 0;
			}
			
			/* Service item container with proper layering */
			.service-item-container {
				background-color: #fff;
				position: relative;
			}

			/* Direct styling for service items in search */
			.category-label {
				display: inline-block;
				background-color: #1e3a8a;
				color: white;
				font-weight: bold;
				padding: 4px 10px;
				border-radius: 4px;
				margin-bottom: 10px;
				font-size: 12px;
			}
			
			/* Target service items directly */
			li > [class*="TikTok"], 
			li > [class*="Facebook"], 
			li > [class*="Telegram"],
			li > [class*="YouTube"] {
				background-color: white;
				padding: 12px 15px;
				border-radius: 4px;
				margin-bottom: 5px;
				border-left: 3px solid #1e3a8a;
				cursor: pointer;
				display: block;
				box-shadow: 0 1px 3px rgba(0,0,0,0.05);
				position: relative;
				z-index: 10;
			}
			
			li > [class*="TikTok"]:hover, 
			li > [class*="Facebook"]:hover, 
			li > [class*="Telegram"]:hover,
			li > [class*="YouTube"]:hover {
				background-color: #f5f8ff;
			}
			
			/* Provider name styling */
			[class*="TikTok"] > .fa-diamond,
			[class*="Facebook"] > .fa-diamond,
			[class*="Telegram"] > .fa-diamond,
			[class*="YouTube"] > .fa-diamond {
				color: #ff4d4f;
				margin-right: 5px;
				font-size: 14px;
			}
			
			/* Platform colors */
			[class*="TikTok"]:first-of-type {
				color: #ff0050;
				font-weight: bold;
			}
			
			[class*="Facebook"]:first-of-type {
				color: #1877F2;
				font-weight: bold;
			}
			
			[class*="Telegram"]:first-of-type {
				color: #0088cc;
				font-weight: bold;
			}
			
			[class*="YouTube"]:first-of-type {
				color: #FF0000;
				font-weight: bold;
			}
			
			/* Feature tags */
			.refill, .non-drop, .fastest, .instant {
				display: inline-block;
				padding: 2px 5px;
				border-radius: 3px;
				font-size: 10px;
				font-weight: bold;
				margin: 0 3px;
				text-transform: uppercase;
			}
			
			.refill {
				background-color: #e6f7ff;
				color: #0070f3;
			}
			
			.non-drop {
				background-color: #e6fff0;
				color: #00b37e;
			}
			
			.emergency {
				background-color: #fff0f0;
				color: #ff4d4f;
			}
			
			.fastest {
				background-color: #fff8e6;
				color: #d4a700;
			}
			
			/* Result list container */
			#category-data, 
			#service-data,
			.service-list,
			.search-result-list {
				background-color: white;
				border-radius: 8px;
				box-shadow: 0 2px 10px rgba(0,0,0,0.05);
				padding: 15px;
				margin-bottom: 20px;
				list-style-type: none;
				position: relative;
				z-index: 5;
				border: 1px solid #e0e0e0;
			}
			
			/* Service price */
			.service-price,
			[class*="per 1000"] {
				float: right;
				color: #28a745;
				font-weight: bold;
			}

			/* Custom Show more button */
			.show-more-btn {
				display: flex;
				align-items: center;
				padding: 12px 20px;
				color: rgba(255,255,255,0.85);
				text-decoration: none;
				transition: all 0.3s;
				font-size: 14px;
				border-left: 3px solid transparent;
				cursor: pointer;
				background: transparent;
				width: 100%;
			}
			
			.show-more-btn:hover {
				background: rgba(255,255,255,0.08);
				color: #e0e7ff;
				border-left: 3px solid #4f46e5;
			}
			
			.show-more-btn i {
				width: 20px;
				text-align: center;
				margin-right: 15px;
				font-size: 16px;
			}
			
			/* More menu items styling */
			.more-menu {
				display: none;
				padding: 0;
				margin: 0;
				list-style: none;
				background: transparent !important;
			}
			
			.more-menu .nav-item {
				background: transparent !important;
			}
			
			.more-menu a {
				background: transparent !important;
				display: flex;
				align-items: center;
				padding: 10px 20px 10px 38px;
				color: rgba(255,255,255,0.85);
				text-decoration: none;
				transition: all 0.3s;
				font-size: 13px;
				border-left: 3px solid transparent;
			}
			
			.more-menu a:hover {
				background: rgba(255,255,255,0.08) !important;
				color: #e0e7ff;
				border-left: 3px solid #4f46e5;
			}
			
			.more-menu a i {
				width: 20px;
				text-align: center;
				margin-right: 15px;
				font-size: 14px;
			}
		</style>
		<!-- Custom Styles for Search Page and Homepage -->
		<style>
			/* Better Category Selection Styles */
			.category-filter {
				background-color: #f8f9fa !important;
				border: 1px solid #dee2e6 !important;
				color: #212529 !important;
				border-radius: 4px;
				padding: 8px 15px;
			}
			
			.category-dropdown .dropdown-menu {
				max-height: 300px;
				overflow-y: auto;
				padding: 10px 0;
				border-radius: 4px;
				box-shadow: 0 5px 15px rgba(0,0,0,0.1);
			}
			
			.category-dropdown .dropdown-item {
				padding: 8px 20px;
				color: #333 !important;
				transition: background-color 0.2s;
			}
			
			.category-dropdown .dropdown-item:hover {
				background-color: #f5f5f5;
			}
			
			.category-dropdown .dropdown-item.active {
				background-color: #007bff;
				color: white !important;
			}
			
			/* Inactive Categories */
			.category-inactive {
				color: #999 !important;
				font-style: italic;
			}
			
			.category-active-filter, .category-inactive-filter {
				display: inline-block;
				padding: 3px 10px;
				margin-right: 10px;
				margin-bottom: 10px;
				border-radius: 15px;
				font-size: 12px;
				cursor: pointer;
			}
			
			.category-active-filter {
				background: #e8f4ff;
				color: #0d6efd;
				border: 1px solid #b8daff;
			}
			
			.category-inactive-filter {
				background: #f8f9fa;
				color: #6c757d;
				border: 1px solid #e2e6ea;
			}
			
			/* Cart Improvements */
			.cart-count {
				background-color: #fd7e14;
				color: white;
				border-radius: 50%;
				width: 20px;
				height: 20px;
				display: inline-flex;
				align-items: center;
				justify-content: center;
				font-size: 11px;
				font-weight: bold;
				margin-left: 5px;
				position: relative;
				top: -2px;
			}
			
			/* Improved Search */
			.search-input {
				border: 1px solid #ced4da;
				border-radius: 4px;
				padding: 8px 12px;
				width: 100%;
				transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
			}
			
			.search-input:focus {
				border-color: #80bdff;
				outline: 0;
				box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
			}
			
			/* Homepage Featured Sections */
			.featured-section {
				margin: 30px 0;
				padding: 20px;
				border-radius: 8px;
				background: white;
				box-shadow: 0 2px 10px rgba(0,0,0,0.05);
				transition: transform 0.3s, box-shadow 0.3s;
			}
			
			.featured-section:hover {
				transform: translateY(-5px);
				box-shadow: 0 5px 15px rgba(0,0,0,0.1);
			}
			
			.featured-section h3 {
				color: #333;
				font-size: 22px;
				margin-bottom: 5px;
			}
			
			.featured-section p {
				color: #666;
				font-size: 14px;
				margin-bottom: 15px;
			}
			
			.featured-section.electronics {
				border-top: 4px solid #007bff;
			}
			
			.featured-section.fashion {
				border-top: 4px solid #fd7e14;
			}
			
			.featured-section.home {
				border-top: 4px solid #28a745;
			}
			
			.featured-section.books {
				border-top: 4px solid #dc3545;
			}
			
			.featured-section a.btn {
				display: inline-block;
				padding: 6px 12px;
				background: #f8f9fa;
				color: #333;
				text-decoration: none;
				border-radius: 4px;
				font-size: 14px;
				border: 1px solid #ddd;
				transition: all 0.2s;
			}
			
			.featured-section a.btn:hover {
				background: #e9ecef;
			}
			
			/* Select All Categories Button */
			.select-all-btn {
				cursor: pointer;
				color: #0d6efd;
				text-decoration: none;
				font-weight: 500;
				display: inline-block;
				padding: 5px 10px;
				border-radius: 4px;
				background-color: #e8f4ff;
				margin-bottom: 10px;
			}
			
			.select-all-btn:hover {
				background-color: #d0e5ff;
			}
		</style>
	</head>
	<body {% if not user['auth'] %}class="non-auth-body"{% endif %}>
		{% if user['auth'] %}
			<!-- Sidebar for authenticated users -->
			<div id="sidebar" class="sidebar">
				<div class="sidebar-header">
					<div class="user-profile">
						<a href="{{ page_url('addfunds') }}" class="add-funds-button" title="Add Funds">
							<i class="fa fa-plus"></i>
						</a>
						<div class="user-avatar-wrapper">
						<img src="https://storage.perfectcdn.com/hmz1fi/xnihm05yyakf6ouk.png" alt="User Avatar" class="user-avatar">
							<div class="user-status"></div>
						</div>
						<div class="user-name">{{ user['username'] }}</div>
						<div class="user-email">{{ user['email']|default('<EMAIL>') }}</div>
						<div class="user-balance">
							<i class="fa fa-wallet"></i>
							<span class="amount">${{ user['balance'] }}</span>
							{% if user['is_premium'] %}
							<span class="badge-premium">Premium</span>
							{% endif %}
						</div>
					</div>
				</div>
				
				<div class="sidebar-menu">
					<ul class="nav flex-column">
						<li class="nav-item">
							<a href="{{ page_url('order/new') }}" class="nav-link {% if page['controller'] == 'order' and page['action'] == 'new' %}active{% endif %}">
						<i class="fa fa-shopping-cart"></i>
						<span class="menu-text">New order</span>
					</a>
						</li>
						<li class="nav-item">
							<a href="{{ page_url('orders') }}" class="nav-link {% if page['controller'] == 'orders' %}active{% endif %}">
								<i class="fa fa-list-alt"></i>
								<span class="menu-text">My Orders</span>
								{% if countorders %}
								<span class="badge badge-orders">{{ countorders }}</span>
						{% endif %}
					</a>
						</li>
						<li class="nav-item">
							<a href="{{ page_url('services') }}" class="nav-link {% if page['controller'] == 'services' %}active{% endif %}">
								<i class="fa fa-th-list"></i>
								<span class="menu-text">Services</span>
							</a>
						</li>
						<li class="nav-item">
							<a href="{{ page_url('addfunds') }}" class="nav-link {% if page['controller'] == 'addfunds' %}active{% endif %}">
						<i class="fa fa-money"></i>
						<span class="menu-text">Add funds</span>
					</a>
						</li>
						<li class="nav-item">
							<a href="{{ page_url('tickets') }}" class="nav-link {% if page['controller'] == 'tickets' %}active{% endif %}">
						<i class="fa fa-ticket"></i>
						<span class="menu-text">Tickets</span>
								{% if counttickets %}
								<span class="badge badge-tickets">{{ counttickets }}</span>
						{% endif %}
					</a>
						</li>
						<li class="nav-item">
							<a href="javascript:void(0);" class="show-more-btn" id="sidebarMoreBtn" style="z-index: 100;">
								<i class="fa fa-angle-down"></i> <span class="menu-text">{% if lang is defined %}{{ lang('general.show_more') }}{% else %}Show more{% endif %}</span>
							</a>
						</li>
					</ul>

					<!-- Show more menu items (hidden by default) -->
					<ul class="nav flex-column more-menu" id="sidebarMoreMenu" style="display: none; z-index: 99;">
						<!-- More menu items -->
						{% if additional_menu_items is defined and additional_menu_items %}
							{% for item in additional_menu_items %}
								<li class="nav-item">
									<a href="{{ item.link }}" class="{% if item.current %} active {% endif %}">
										<i class="{{ item.icon }}"></i> <span class="menu-text">{{ item.title }}</span>
									</a>
								</li>
							{% endfor %}
						{% else %}
							<!-- Default menu items -->
							<li class="nav-item">
								<a href="{{ page_url('index') }}" class="{% if page['controller'] == 'index' %}active{% endif %}">
									<i class="fa fa-th-large"></i> <span class="menu-text">Dashboard</span>
								</a>
							</li>
							<li class="nav-item">
								<a href="{{ page_url('api') }}" class="{% if page['controller'] == 'api' %}active{% endif %}">
									<i class="fa fa-code"></i> <span class="menu-text">API</span>
								</a>
							</li>
							<li class="nav-item">
								<a href="{{ page_url('affiliates') }}" class="{% if page['controller'] == 'affiliates' %}active{% endif %}">
									<i class="fa fa-users"></i> <span class="menu-text">Affiliates</span>
								</a>
							</li>
							<li class="nav-item">
								<a href="{{ page_url('child-panel') }}" class="{% if page['controller'] == 'child-panel' %}active{% endif %}">
									<i class="fa fa-user-plus"></i> <span class="menu-text">Child panel</span>
								</a>
							</li>
							<li class="nav-item">
								<a href="{{ page_url('faq') }}" class="{% if page['controller'] == 'faq' %}active{% endif %}">
									<i class="fa fa-question-circle"></i> <span class="menu-text">FAQ</span>
								</a>
							</li>
							<li class="nav-item">
								<a href="{{ page_url('updates') }}" class="{% if page['controller'] == 'updates' %}active{% endif %}">
									<i class="fa fa-bell"></i> <span class="menu-text">Updates</span>
								</a>
							</li>
							<li class="nav-item">
								<a href="{{ page_url('terms') }}" class="{% if page['controller'] == 'terms' %}active{% endif %}">
									<i class="fa fa-file-text"></i> <span class="menu-text">Terms</span>
								</a>
							</li>
						{% endif %}
					</ul>
				</div>
			</div>
			
			<!-- Toggle sidebar button -->
			<button id="toggleSidebar" class="toggle-sidebar" type="button" aria-label="Toggle Sidebar">
				<i class="fa fa-bars" aria-hidden="true"></i>
			</button>
			
			<!-- Auth User Header -->
			<div id="authHeader" class="auth-header">
				<div class="auth-logo">
					<a href="{{ page_url('index') }}"><img src="{{ asset_url }}logo.png" alt="{{ site_name }}"></a>
				</div>
				<div class="auth-actions">
					<!-- Telegram Button with Dropdown -->
					<div class="auth-dropdown">
						<a href="javascript:void(0);" class="auth-icon-link" title="Telegram">
							<i class="fa fa-paper-plane"></i>
						</a>
						<div class="dropdown-content telegram-dropdown">
							<a href="https://t.me/your_channel" target="_blank">
								<i class="fa fa-paper-plane"></i> Join Channel
							</a>
							<a href="{{ page_url('contact') }}">
								<i class="fa fa-headphones"></i> Contact Us
							</a>
						</div>
					</div>
					
					<!-- User Account Dropdown -->
					<div class="auth-dropdown">
						<a href="javascript:void(0);" class="auth-icon-link" id="userDropdownToggle">
							<i class="fa fa-user"></i>
						</a>
						<div class="dropdown-content user-dropdown" id="userDropdownContent">
							<div class="user-dropdown-header">
								<img src="https://storage.perfectcdn.com/hmz1fi/xnihm05yyakf6ouk.png" alt="Profile" class="user-dropdown-avatar">
								<div class="user-dropdown-info">
									<div class="user-dropdown-name">{{ user['username'] }}</div>
									<div class="user-dropdown-balance">
										<i class="fa fa-wallet"></i>
										${{ user['balance'] }}
									</div>
								</div>
							</div>
							<a href="{{ page_url('account') }}">
								<i class="fa fa-cog"></i> Account Settings
							</a>
							<a href="{{ page_url('logout') }}" style="color: #dc3545;">
								<i class="fa fa-sign-out"></i> Logout
							</a>
						</div>
					</div>
					<!-- Dark Mode Toggle -->
					<div class="theme-toggle">
						<button id="theme-toggle" class="theme-toggle-btn" aria-label="Toggle dark mode">
							<i class="fa fa-moon"></i>
						</button>
					</div>
				</div>
			</div>
			
			<div id="mainContent" class="main-content">
				{{ content }}
			</div>
		{% else %}
			<!-- Custom Header for non-auth users -->
			<header class="simple-header">
				<nav>
					<div class="nav-logo">
						<a href="{{ page_url('index') }}">
							<img src="{{ asset_url }}logo.png" alt="{{ site_name }}">
						</a>
					</div>
					<div class="nav-links">
						<a href="{{ page_url('signin') }}"><i class="fa fa-sign-in"></i> Sign in</a>
						<a href="{{ page_url('services') }}"><i class="fa fa-th-list"></i> Services</a>
						<a href="{{ page_url('tutorials') }}"><i class="fa fa-book"></i> Tutorials</a>
						<a href="{{ page_url('tools') }}"><i class="fa fa-wrench"></i> Free Tools</a>
						<a href="{{ page_url('about') }}"><i class="fa fa-info-circle"></i> About</a>
						<a href="{{ page_url('blog') }}"><i class="fa fa-rss"></i> Blog</a>
						<a href="{{ page_url('contact') }}"><i class="fa fa-envelope"></i> Contact</a>
						<a href="{{ page_url('signup') }}" class="signup-btn"><i class="fa fa-user-plus"></i> SignUp</a>
					</div>
					<button id="mobileMenuToggle" class="mobile-toggle">
						<span></span>
						<span></span>
						<span></span>
					</button>
				</nav>
			</header>

			<style>
			.simple-header {
				background: #fff;
				padding: 15px 0;
				box-shadow: 0 1px 3px rgba(0,0,0,0.1);
				position: fixed;
				width: 100%;
				top: 0;
				z-index: 1000;
			}

			.simple-header nav {
				max-width: 1200px;
				margin: 0 auto;
				padding: 0 15px;
				display: flex;
				justify-content: space-between;
				align-items: center;
			}

			.nav-logo img {
				height: 35px;
				width: auto;
			}

			.nav-links {
				display: flex;
				align-items: center;
				gap: 25px;
			}

			.simple-header a {
				color: #444;
				text-decoration: none;
				font-size: 14px;
				transition: all 0.2s;
				display: flex;
				align-items: center;
				gap: 5px;
			}

			.simple-header a:hover {
				color: #007bff;
			}

			.simple-header .signup-btn {
				background: #007bff;
				color: #fff;
				padding: 8px 16px;
				border-radius: 4px;
				transition: background-color 0.2s;
			}

			.simple-header .signup-btn:hover {
				background: #0056b3;
				color: #fff;
			}

			.simple-header i {
				font-size: 14px;
			}
			
			.simple-header .mobile-toggle {
				display: none;
				background: none;
				border: none;
				cursor: pointer;
				padding: 0;
				width: 30px;
				height: 20px;
				position: relative;
				z-index: 1001;
			}

			@media (max-width: 768px) {
				.nav-links {
					display: none;
				}
				.nav-logo {
					margin: 0;
				}
				.simple-header nav {
					justify-content: space-between;
				}
				.simple-header .mobile-toggle {
					display: block;
				}
			}
			
			/* Ensure mobile menu appears below header with space for fixed header */
			.mobile-menu {
				top: 65px;
				padding-top: 10px;
			}
			
			body.non-auth-body {
				padding-top: 65px; /* Add padding to account for fixed header */
			}
			
			/* Mobile toggle button styles */
			.simple-header .mobile-toggle span {
				display: block;
				width: 100%;
				height: 2px;
				background-color: #444;
				position: absolute;
				left: 0;
				transition: all 0.3s;
			}
			
			.simple-header .mobile-toggle span:nth-child(1) {
				top: 0;
			}
			
			.simple-header .mobile-toggle span:nth-child(2) {
				top: 9px;
			}
			
			.simple-header .mobile-toggle span:nth-child(3) {
				bottom: 0;
			}
			
			.simple-header .mobile-toggle.open span:nth-child(1) {
				transform: rotate(45deg);
				top: 9px;
			}
			
			.simple-header .mobile-toggle.open span:nth-child(2) {
				opacity: 0;
			}
			
			.simple-header .mobile-toggle.open span:nth-child(3) {
				transform: rotate(-45deg);
				bottom: 9px;
			}
			
			/* Dark mode styles for non-auth header */
			body.dark-mode .simple-header {
				background: #1a202c;
				box-shadow: 0 1px 3px rgba(0,0,0,0.3);
			}
			
			body.dark-mode .simple-header a {
				color: #e2e8f0;
			}
			
			body.dark-mode .simple-header a:hover {
				color: #63b3ed;
			}
			
			body.dark-mode .simple-header .signup-btn {
				background: #2b6cb0;
				color: #e2e8f0;
			}
			
			body.dark-mode .simple-header .signup-btn:hover {
				background: #2c5282;
			}
			
			body.dark-mode .simple-header .mobile-toggle span {
				background-color: #e2e8f0;
			}
			
			body.dark-mode .mobile-menu {
				background-color: #1a202c;
				box-shadow: 0 2px 5px rgba(0,0,0,0.3);
			}
			
			body.dark-mode .mobile-menu a {
				color: #e2e8f0;
			}
			
			body.dark-mode .mobile-menu a:hover {
				color: #63b3ed;
			}
			
			body.dark-mode .mobile-menu .btn-signup {
				background: #2b6cb0;
				color: #e2e8f0;
			}
			
			body.dark-mode .mobile-menu .btn-signup:hover {
				background: #2c5282;
			}
			</style>
			
			<!-- Mobile Menu -->
			<div class="mobile-menu" id="mobileMenu">
				<a href="{{ page_url('signin') }}" class="nav-link">Sign in</a>
				<a href="{{ page_url('services') }}" class="nav-link">Services</a>
				<a href="{{ page_url('tutorials') }}" class="nav-link">Tutorials</a>
				<a href="{{ page_url('tools') }}" class="nav-link">Free Tools</a>
				<a href="{{ page_url('about') }}" class="nav-link">About Us</a>
				<a href="{{ page_url('blog') }}" class="nav-link">Blog Post</a>
				<a href="{{ page_url('contact') }}" class="nav-link">Contact us</a>
				<a href="{{ page_url('signup') }}" class="btn-signup">Sign Up</a>
			</div>
		{% endif %}
		
		<!-- Main variables *content* -->
		{% if not user['auth'] %}
		{{ content }}
		{% endif %}

		<!-- Notifications wrapper -->
		<div id="notify-wrapper" class="alert alert-success hidden" style="display: none;"></div>

		{% if not user['auth'] %}
		<!-- Footer Section (only for non-authenticated users) -->
		<footer style="background-color: #f8f9fa; padding: 40px 0; font-family: Arial, sans-serif; border-top: 1px solid #e0e0e0;">
			<div style="max-width: 1200px; margin: 0 auto; padding: 0 15px; display: flex; flex-wrap: wrap; justify-content: space-between;">
				<!-- Logo and Contact Info -->
				<div style="flex: 1; min-width: 250px; margin-bottom: 20px;">
					<div style="margin-bottom: 15px;">
						<img src="https://storage.perfectcdn.com/axz9n1/2zqoww0i506t0dom.png" alt="{{ site['name'] }}" style="max-width: 150px;">
					</div>
					<p style="margin: 10px 0; display: flex; align-items: center;">
						<span style="display: inline-block; margin-right: 10px; color: #555;">📞</span>
						Call Us: +1 (718) 217-6465
					</p>
					<p style="margin: 10px 0; display: flex; align-items: center;">
						<span style="display: inline-block; margin-right: 10px; color: #555;">✉️</span>
						info@{{ site['domain'] }}
					</p>
					<p style="margin: 10px 0; display: flex; align-items: center;">
						<span style="display: inline-block; margin-right: 10px; color: #555;">📍</span>
						2 Walden Galeria, Buffalo - 14225, New York, United States
					</p>
				</div>

				<!-- Quick Links -->
				<div style="flex: 1; min-width: 180px; margin-bottom: 20px;">
					<h4 style="font-size: 16px; font-weight: bold; margin-bottom: 15px; color: #333;">Quick Links</h4>
					<ul style="list-style: none; padding: 0; margin: 0;">
						<li style="margin-bottom: 8px;"><a href="{{ page_url('index') }}" style="color: #333; text-decoration: none; font-size: 14px;">Home</a></li>
						<li style="margin-bottom: 8px;"><a href="/about" style="color: #333; text-decoration: none; font-size: 14px;">About Us</a></li>
						<li style="margin-bottom: 8px;"><a href="/services" style="color: #333; text-decoration: none; font-size: 14px;">Our Services</a></li>
						<li style="margin-bottom: 8px;"><a href="/blog" style="color: #333; text-decoration: none; font-size: 14px;">Read Our Blog</a></li>
						<li style="margin-bottom: 8px;"><a href="/api" style="color: #333; text-decoration: none; font-size: 14px;">API</a></li>
					</ul>
				</div>

				<!-- Our Services -->
				<div style="flex: 1; min-width: 180px; margin-bottom: 20px;">
					<h4 style="font-size: 16px; font-weight: bold; margin-bottom: 15px; color: #333;">Our Services</h4>
					<ul style="list-style: none; padding: 0; margin: 0;">
						<li style="margin-bottom: 8px;"><a href="/services/instagram" style="color: #333; text-decoration: none; font-size: 14px;">Instagram SMM Panel</a></li>
						<li style="margin-bottom: 8px;"><a href="/services/facebook" style="color: #333; text-decoration: none; font-size: 14px;">Facebook SMM Panel</a></li>
						<li style="margin-bottom: 8px;"><a href="/services/youtube" style="color: #333; text-decoration: none; font-size: 14px;">YouTube SMM Panel</a></li>
						<li style="margin-bottom: 8px;"><a href="/services/twitter" style="color: #333; text-decoration: none; font-size: 14px;">X-Twitter SMM Panel</a></li>
						<li style="margin-bottom: 8px;"><a href="/services/telegram" style="color: #333; text-decoration: none; font-size: 14px;">Telegram SMM Panel</a></li>
						<li style="margin-bottom: 8px;"><a href="/services/tiktok" style="color: #333; text-decoration: none; font-size: 14px;">TikTok SMM Panel</a></li>
					</ul>
				</div>

				<!-- Company -->
				<div style="flex: 1; min-width: 180px; margin-bottom: 20px;">
					<h4 style="font-size: 16px; font-weight: bold; margin-bottom: 15px; color: #333;">Company</h4>
					<ul style="list-style: none; padding: 0; margin: 0;">
						<li style="margin-bottom: 8px;"><a href="/terms" style="color: #333; text-decoration: none; font-size: 14px;">Terms Of Services</a></li>
						<li style="margin-bottom: 8px;"><a href="/privacy" style="color: #333; text-decoration: none; font-size: 14px;">Privacy Policy</a></li>
						<li style="margin-bottom: 8px;"><a href="/refund" style="color: #333; text-decoration: none; font-size: 14px;">Refund Policy</a></li>
						<li style="margin-bottom: 8px;"><a href="/faq" style="color: #333; text-decoration: none; font-size: 14px;">Frequently Asked Questions</a></li>
						<li style="margin-bottom: 8px;"><a href="/contact" style="color: #333; text-decoration: none; font-size: 14px;">Contact Us</a></li>
					</ul>
				</div>
			</div>

			<!-- Copyright Section -->
			<div style="text-align: center; padding: 15px 0; border-top: 1px solid #e0e0e0; margin-top: 10px;">
				<p style="margin: 0; color: #666; font-size: 14px;">© {{ "now"|date("Y") }} {{ site['domain'] }}. All Rights Reserved.</p>
			</div>
		</footer>
		{% endif %}

		{{ site['custom_footer'] }}

		{% for script in site['scripts'] %}
			<script type="text/javascript" {% if script['src'] %} src="{{ script['src'] }}" {% endif %}>
				{% if script['code'] %}
{{ script['code'] }}
{% endif %}
			</script>
		{% endfor %}
		
		{% if not user['auth'] %}
		<script>
			document.addEventListener('DOMContentLoaded', function() {
				var mobileToggle = document.getElementById('mobileMenuToggle');
				var mobileMenu = document.getElementById('mobileMenu');
				
				if (mobileToggle && mobileMenu) {
					mobileToggle.addEventListener('click', function() {
						mobileToggle.classList.toggle('open');
						mobileMenu.classList.toggle('open');
					});
				}
				
				// Check if Font Awesome is loaded properly
				var span = document.createElement('span');
				span.className = 'fa';
				span.style.display = 'none';
				document.body.insertBefore(span, document.body.firstChild);
				
				var loaded = window.getComputedStyle(span).fontFamily.match(/fontawesome/i) !== null;
				document.body.removeChild(span);
				
				if (!loaded) {
					// If Font Awesome didn't load, try to reload it
					var link = document.createElement('link');
					link.rel = 'stylesheet';
					link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css';
					document.head.appendChild(link);
				}
			});
		</script>
		{% endif %}

		<!-- Script for authenticated users -->
		{% if user['auth'] %}
		<script>
			document.addEventListener('DOMContentLoaded', function() {
				// Get all necessary elements
				const sidebar = document.getElementById('sidebar');
				const toggleBtn = document.getElementById('toggleSidebar');
				const mainContent = document.getElementById('mainContent');
				const authHeader = document.getElementById('authHeader');
				
				console.log("Sidebar toggle setup");
				
				// Check if elements exist
				if (!sidebar || !toggleBtn) {
					console.error("Sidebar or toggle button not found");
					return;
				}
				
				// Create sidebar overlay for mobile if it doesn't exist
				if (!document.querySelector('.sidebar-overlay')) {
					const overlay = document.createElement('div');
					overlay.className = 'sidebar-overlay';
					document.body.appendChild(overlay);
					
					// Add click event to close sidebar when overlay is clicked
					overlay.addEventListener('click', function() {
						if (window.innerWidth <= 768) {
							document.body.classList.remove('sidebar-open');
							sidebar.classList.add('collapsed');
							localStorage.setItem('sidebarCollapsed', 'true');
						}
					});
				}
				
				// Function to toggle sidebar
				function toggleSidebar(event) {
					if (event) {
						event.preventDefault();
						event.stopPropagation();
					}
					
					console.log("Toggle sidebar clicked");
					
					if (window.innerWidth <= 768) {
						// Mobile behavior
						document.body.classList.toggle('sidebar-open');
						
						if (document.body.classList.contains('sidebar-open')) {
							sidebar.classList.remove('collapsed');
						} else {
							sidebar.classList.add('collapsed');
						}
					} else {
						// Desktop behavior
						sidebar.classList.toggle('collapsed');
					}
					
					// Save state to localStorage
					localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
					
					// Dispatch custom event
					const event = new CustomEvent('sidebarToggled', {
						detail: { collapsed: sidebar.classList.contains('collapsed') }
					});
					document.dispatchEvent(event);
				}
				
				// Initialize sidebar state from localStorage
				const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
				if (sidebarCollapsed) {
					sidebar.classList.add('collapsed');
				} else {
					sidebar.classList.remove('collapsed');
				}
				
				// Add click event to toggle button
				toggleBtn.addEventListener('click', toggleSidebar);
				console.log("Toggle button listener added");
				
				// Handle dropdowns
				const dropdowns = document.querySelectorAll('.auth-dropdown');
				
				dropdowns.forEach(function(dropdown) {
					const link = dropdown.querySelector('.auth-icon-link');
					const content = dropdown.querySelector('.dropdown-content');
					
					if (link && content) {
						// Remove any existing click listeners
						link.removeEventListener('click', handleDropdownClick);
						
						// Add new click listener
						link.addEventListener('click', handleDropdownClick);
					}
				});
				
				function handleDropdownClick(e) {
					e.preventDefault();
					e.stopPropagation();
					
					const dropdown = this.closest('.auth-dropdown');
					const content = dropdown.querySelector('.dropdown-content');
					
					// Close all other dropdowns
					dropdowns.forEach(function(otherDropdown) {
						if (otherDropdown !== dropdown) {
							const otherContent = otherDropdown.querySelector('.dropdown-content');
							if (otherContent) {
								otherContent.style.display = 'none';
							}
						}
					});
					
					// Toggle this dropdown
					if (content) {
						content.style.display = content.style.display === 'block' ? 'none' : 'block';
					}
				}
				
				// Close dropdowns when clicking outside
				document.addEventListener('click', function(e) {
					dropdowns.forEach(function(dropdown) {
						if (!dropdown.contains(e.target)) {
							const content = dropdown.querySelector('.dropdown-content');
							if (content) {
								content.style.display = 'none';
							}
						}
					});
				});
				
				// Prevent dropdown content clicks from closing the dropdown
				document.querySelectorAll('.dropdown-content').forEach(function(content) {
					content.addEventListener('click', function(e) {
						e.stopPropagation();
					});
				});
				
				// Initial responsive check
				function handleResponsiveLayout() {
					if (window.innerWidth <= 768) {
						if (!document.body.classList.contains('sidebar-open')) {
								sidebar.classList.add('collapsed');
						}
					}
				}
				
				// Initial check
				handleResponsiveLayout();
				
				// Update on window resize
				window.addEventListener('resize', function() {
					handleResponsiveLayout();
					
					// On desktop, fix any inconsistencies
					if (window.innerWidth > 768) {
						document.body.classList.remove('sidebar-open');
					}
				});
				
				// Fix for show more menu toggle
				const showMoreBtn = document.querySelector('.show-more-btn');
				const moreMenu = document.querySelector('.more-menu');
				
				if (showMoreBtn && moreMenu) {
					console.log("Show more button found");
					
					// Ensure the menu is initially hidden and has proper z-index
					moreMenu.style.display = 'none';
					if (!moreMenu.style.zIndex) {
						moreMenu.style.zIndex = '100';
					}
					
					showMoreBtn.addEventListener('click', function(e) {
						e.preventDefault();
						e.stopPropagation();
						
						console.log("Show more button clicked");
						
						// Toggle menu visibility
						const isVisible = moreMenu.style.display === 'block';
						moreMenu.style.display = isVisible ? 'none' : 'block';
						
						// Toggle icon
						const icon = showMoreBtn.querySelector('i');
						if (icon) {
							icon.className = isVisible ? 'fa fa-angle-down' : 'fa fa-angle-up';
						}
					});
					
					// Prevent closing when clicking inside the more menu
					moreMenu.addEventListener('click', function(e) {
						e.stopPropagation();
					});
					
					// Close more menu when clicking elsewhere
					document.addEventListener('click', function(e) {
						if (!showMoreBtn.contains(e.target) && !moreMenu.contains(e.target)) {
							moreMenu.style.display = 'none';
							
							// Reset icon
							const icon = showMoreBtn.querySelector('i');
							if (icon) {
								icon.className = 'fa fa-angle-down';
							}
						}
					});
				} else {
					console.error("Show more button or menu not found");
				}
				
				// Make all sidebar links clickable
				const sidebarLinks = sidebar.querySelectorAll('a');
				sidebarLinks.forEach(function(link) {
					// Ensure all links are properly clickable
					link.addEventListener('click', function(e) {
						e.stopPropagation();
					});
				});
				
				// Initialize Select2 for dropdowns if available
				if (typeof $ !== 'undefined' && typeof $.fn !== 'undefined' && typeof $.fn.select2 !== 'undefined') {
					$('[data-select="true"]').each(function() {
						$(this).select2({
							width: '100%',
							minimumResultsForSearch: 10
						});
					});
				}
			});
		</script>
		{% endif %}

		<!-- Add this right before the closing body tag
		// This script will check what's happening with the show more menu -->
		<script>
		// Add this right before the closing body tag
		// This script will check what's happening with the show more menu
		document.addEventListener('DOMContentLoaded', function() {
		  console.log('Debug script loaded to check sidebar functionality');
		  
		  // Debug sidebar
		  const sidebar = document.getElementById('sidebar');
		  const toggleBtn = document.getElementById('toggleSidebar');
		  const showMoreBtn = document.querySelector('.show-more-btn');
		  const moreMenu = document.querySelector('.more-menu');
		  
		  if (sidebar) {
			console.log('Sidebar found:', sidebar);
		  } else {
			console.error('Sidebar not found!');
		  }
		  
		  if (toggleBtn) {
			console.log('Toggle button found:', toggleBtn);
			
			// Add additional click handler to check if events are firing
			toggleBtn.addEventListener('click', function() {
			  console.log('Toggle button clicked in debug script');
			});
		  } else {
			console.error('Toggle button not found!');
		  }
		  
		  if (showMoreBtn) {
			console.log('Show more button found:', showMoreBtn);
			console.log('Show more button parent:', showMoreBtn.parentElement);
			
			// Add additional click handler to check if events are firing
			showMoreBtn.addEventListener('click', function(e) {
			  console.log('Show more button clicked in debug script');
			  console.log('Event prevented default:', e.defaultPrevented);
			  
			  // Check if the more menu is toggled
			  if (moreMenu) {
				console.log('More menu style display after click:', moreMenu.style.display);
			  }
			});
		  } else {
			console.error('Show more button not found!');
		  }
		  
		  if (moreMenu) {
			console.log('More menu found:', moreMenu);
			console.log('More menu style display:', moreMenu.style.display);
		  } else {
			console.error('More menu not found!');
		  }
		  
		  // Fix for the show more menu if nothing else works
		  if (showMoreBtn && moreMenu) {
			// Create a brand new click handler that replaces any existing ones
			showMoreBtn.onclick = function(e) {
			  e.preventDefault();
			  e.stopPropagation();
			  console.log('Show more button clicked - emergency handler');
			  
			  // Force toggle display
			  if (moreMenu.style.display === 'block') {
				moreMenu.style.display = 'none';
				
				if (showMoreBtn.querySelector('i')) {
				  showMoreBtn.querySelector('i').className = 'fa fa-angle-down';
				}
			  } else {
				moreMenu.style.display = 'block';
				
				if (showMoreBtn.querySelector('i')) {
				  showMoreBtn.querySelector('i').className = 'fa fa-angle-up';
				}
			  }
			};
		  }
		});
		</script>

		<!-- Sidebar and Show More Menu Fix Script -->
		<script>
		// This is a dedicated fix for the sidebar toggle and show more menu
		document.addEventListener('DOMContentLoaded', function() {
		  // Fix sidebar toggle functionality
		  const sidebar = document.getElementById('sidebar');
		  const toggleBtn = document.getElementById('toggleSidebar');
		  const mainContent = document.getElementById('mainContent');
		  
		  if (sidebar && toggleBtn) {
			// Replace any existing click handlers with a new one
			toggleBtn.onclick = function(e) {
			  e.preventDefault();
			  e.stopPropagation();
			  
			  console.log('Toggle sidebar clicked (fixed handler)');
			  
			  if (window.innerWidth <= 768) {
				// Mobile behavior
				document.body.classList.toggle('sidebar-open');
				
				if (document.body.classList.contains('sidebar-open')) {
				  sidebar.classList.remove('collapsed');
				} else {
				  sidebar.classList.add('collapsed');
				}
			  } else {
				// Desktop behavior
				sidebar.classList.toggle('collapsed');
			  }
			  
			  // Save state to localStorage
			  localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
			};
			
			// Set initial state
			const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
			if (sidebarCollapsed && !document.body.classList.contains('sidebar-open')) {
			  sidebar.classList.add('collapsed');
			} else {
			  sidebar.classList.remove('collapsed');
			}
		  }
		  
		  // Fix show more menu functionality
		  const showMoreBtn = document.getElementById('sidebarMoreBtn');
		  const moreMenu = document.getElementById('sidebarMoreMenu');
		  
		  if (showMoreBtn && moreMenu) {
			// Replace any existing click handlers with a new one
			showMoreBtn.onclick = function(e) {
			  e.preventDefault();
			  e.stopPropagation();
			  
			  console.log('Show more button clicked (fixed handler)');
			  
			  // Toggle menu visibility
			  if (moreMenu.style.display === 'block') {
				moreMenu.style.display = 'none';
				
				const icon = showMoreBtn.querySelector('i');
				if (icon) {
				  icon.className = 'fa fa-angle-down';
				}
			  } else {
				moreMenu.style.display = 'block';
				
				const icon = showMoreBtn.querySelector('i');
				if (icon) {
				  icon.className = 'fa fa-angle-up';
				}
			  }
			};
			
			// Close more menu when clicking elsewhere
			document.addEventListener('click', function(e) {
			  if (moreMenu.style.display === 'block' && 
				  !showMoreBtn.contains(e.target) && 
				  !moreMenu.contains(e.target)) {
				moreMenu.style.display = 'none';
				
				const icon = showMoreBtn.querySelector('i');
				if (icon) {
				  icon.className = 'fa fa-angle-down';
				}
			  }
			});
			
			// Ensure the menu is initially hidden
			moreMenu.style.display = 'none';
		  }
		  
		  // Create sidebar overlay for mobile if it doesn't exist
		  if (!document.querySelector('.sidebar-overlay')) {
			const overlay = document.createElement('div');
			overlay.className = 'sidebar-overlay';
			document.body.appendChild(overlay);
			
			// Add click event to close sidebar when overlay is clicked
			overlay.addEventListener('click', function() {
			  if (window.innerWidth <= 768) {
				document.body.classList.remove('sidebar-open');
				sidebar.classList.add('collapsed');
			  }
			});
		  }
		  
		  // Make all menu items clickable
		  const menuItems = document.querySelectorAll('#sidebar a.nav-link');
		  menuItems.forEach(function(item) {
			item.addEventListener('click', function(e) {
			  // Don't stop propagation for the show more button
			  if (!this.classList.contains('show-more-btn')) {
				e.stopPropagation();
			  }
			});
		  });
		  
		  // Handle mobile-specific behavior
		  function handleResponsiveLayout() {
			if (window.innerWidth <= 768) {
			  if (!document.body.classList.contains('sidebar-open')) {
				sidebar.classList.add('collapsed');
			  }
			} else {
			  document.body.classList.remove('sidebar-open');
			}
		  }
		  
		  // Initial check
		  handleResponsiveLayout();
		  
		  // Update on window resize
		  window.addEventListener('resize', handleResponsiveLayout);
		});
		</script>

		<script>
		document.addEventListener('DOMContentLoaded', function() {
			// Dropdown functionality
			const userDropdownToggle = document.getElementById('userDropdownToggle');
			const userDropdownContent = document.getElementById('userDropdownContent');

			if (userDropdownToggle && userDropdownContent) {
				userDropdownToggle.addEventListener('click', function(e) {
					e.preventDefault();
					e.stopPropagation();
					
					// Toggle dropdown visibility
					const isVisible = userDropdownContent.style.display === 'block';
					userDropdownContent.style.display = isVisible ? 'none' : 'block';
				});

				// Close dropdown when clicking outside
				document.addEventListener('click', function(e) {
					if (!userDropdownToggle.contains(e.target) && !userDropdownContent.contains(e.target)) {
						userDropdownContent.style.display = 'none';
					}
				});

				// Prevent dropdown from closing when clicking inside
				userDropdownContent.addEventListener('click', function(e) {
					e.stopPropagation();
				});
			}
		});
		</script>

		<style>
		/* Update dropdown styles */
		.auth-dropdown {
			position: relative;
		}

		.dropdown-content {
			position: absolute;
			top: 100%;
			right: 0;
			background-color: white;
			border-radius: 8px;
			box-shadow: 0 5px 15px rgba(0,0,0,0.15);
			min-width: 250px;
			z-index: 1000;
			display: none;
			margin-top: 10px;
		}

		.auth-icon-link {
			cursor: pointer;
		}

		.user-dropdown-header {
			background: linear-gradient(135deg, #1e3a8a 0%, #152b5e 100%);
			padding: 15px;
			border-radius: 8px 8px 0 0;
			display: flex;
			align-items: center;
		}

		.user-dropdown-avatar {
			width: 45px;
			height: 45px;
			border-radius: 50%;
			margin-right: 12px;
			border: 2px solid rgba(255,255,255,0.3);
		}

		.user-dropdown-info {
			flex: 1;
		}

		.user-dropdown-name {
			color: white;
			font-weight: 600;
			font-size: 14px;
			margin-bottom: 4px;
		}

		.user-dropdown-balance {
			color: #e0e7ff;
			font-size: 13px;
			display: flex;
			align-items: center;
			gap: 5px;
		}

		.dropdown-content a {
			padding: 12px 15px;
			display: flex;
			align-items: center;
			color: #333;
			text-decoration: none;
			transition: background-color 0.2s;
		}

		.dropdown-content a:hover {
			background-color: #f5f8ff;
		}

		.dropdown-content a i {
			margin-right: 10px;
			width: 16px;
			text-align: center;
		}

		.dropdown-content a:last-child {
			border-top: 1px solid #eee;
		}
		</style>

		<!-- Dark Mode Styles -->
		<style>
		body.dark-mode {
			background-color: #1a1a2e;
			color: #f0f0f0;
		}
		
		/* Dark mode for sidebar */
		body.dark-mode .sidebar {
			background: linear-gradient(180deg, #0d47a1 0%, #01579b 100%);
			box-shadow: 2px 0 10px rgba(0, 0, 0, 0.5);
		}
		
		body.dark-mode .sidebar-header {
			background-color: rgba(0, 0, 0, 0.2);
			border-bottom: 1px solid rgba(255, 255, 255, 0.05);
		}
		
		body.dark-mode .user-info {
			background-color: rgba(0, 0, 0, 0.15);
		}
		
		body.dark-mode .user-name {
			color: #f0f0f0;
		}
		
		body.dark-mode .user-email {
			color: #b8b8b8;
		}
		
		body.dark-mode .balance-display {
			background-color: rgba(30, 136, 229, 0.25);
			color: #f0f0f0;
		}
		
		body.dark-mode .menu-item {
			color: #d0d0d0;
			border-left: 3px solid transparent;
		}
		
		body.dark-mode .menu-item:hover, 
		body.dark-mode .menu-item.active {
			background-color: rgba(255, 255, 255, 0.07);
			color: #ffffff;
			border-left: 3px solid #1e88e5;
		}
		
		body.dark-mode .menu-item i {
			color: #7a7a8c;
		}
		
		body.dark-mode .menu-item:hover i,
		body.dark-mode .menu-item.active i {
			color: #1e88e5;
		}
		
		/* Dark mode for header */
		body.dark-mode .header {
			background-color: #16213e;
			box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
			border-bottom: 1px solid rgba(255, 255, 255, 0.05);
		}
		
		body.dark-mode .greeting-text {
			color: #f0f0f0;
		}
		
		body.dark-mode .search-bar {
			background-color: rgba(255, 255, 255, 0.07);
			border: 1px solid rgba(255, 255, 255, 0.1);
		}
		
		body.dark-mode .search-bar input {
			color: #f0f0f0;
			background-color: transparent;
		}
		
		body.dark-mode .search-bar input::placeholder {
			color: #7a7a8c;
		}
		
		body.dark-mode .auth-icon-link {
			color: #f0f0f0;
		}
		
		body.dark-mode .auth-icon-link:hover {
			background-color: rgba(255, 255, 255, 0.07);
		}
		
		/* Dark mode for dropdown */
		body.dark-mode .dropdown-content {
			background-color: #16213e;
			box-shadow: 0 8px 16px rgba(0, 0, 0, 0.5);
			border: 1px solid rgba(255, 255, 255, 0.05);
		}
		
		body.dark-mode .user-dropdown-header {
			border-bottom: 1px solid rgba(255, 255, 255, 0.05);
		}
		
		body.dark-mode .user-dropdown-name {
			color: #f0f0f0;
		}
		
		body.dark-mode .user-dropdown-balance {
			color: #b8b8b8;
		}
		
		body.dark-mode .dropdown-content a {
			color: #d0d0d0;
		}
		
		body.dark-mode .dropdown-content a:hover {
			background-color: rgba(255, 255, 255, 0.07);
		}
		
		body.dark-mode .dropdown-content a:last-child {
			border-top: 1px solid rgba(255, 255, 255, 0.05);
		}
		
		/* Dark mode for main content */
		body.dark-mode .main-content {
			background-color: #1a1a2e;
		}
		
		body.dark-mode .card {
			background-color: #16213e;
			box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
			border: 1px solid rgba(255, 255, 255, 0.05);
		}
		
		body.dark-mode .card-header {
			border-bottom: 1px solid rgba(255, 255, 255, 0.05);
			background-color: rgba(0, 0, 0, 0.15);
		}
		
		body.dark-mode .table {
			color: #f0f0f0;
		}
		
		body.dark-mode .table thead th {
			border-bottom: 2px solid rgba(255, 255, 255, 0.05);
			background-color: rgba(0, 0, 0, 0.15);
		}
		
		body.dark-mode .table td, 
		body.dark-mode .table th {
			border-top: 1px solid rgba(255, 255, 255, 0.05);
		}
		
		body.dark-mode .table-hover tbody tr:hover {
			background-color: rgba(255, 255, 255, 0.03);
		}
		
		/* Theme toggle button styles */
		.theme-toggle {
			margin-left: 15px;
		}
		
		.theme-toggle-btn {
			background: transparent;
			border: none;
			color: #666;
			font-size: 18px;
			cursor: pointer;
			width: 40px;
			height: 40px;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			transition: all 0.3s ease;
		}
		
		.theme-toggle-btn:hover {
			background-color: rgba(0, 0, 0, 0.05);
			color: #007bff;
		}
		
		body.dark-mode .theme-toggle-btn {
			color: #f0f0f0;
		}
		
		body.dark-mode .theme-toggle-btn:hover {
			background-color: rgba(255, 255, 255, 0.07);
			color: #4da3ff;
		}
		
		.theme-toggle-btn i.fa-sun {
			display: none;
		}
		
		.theme-toggle-btn i.fa-moon {
			display: inline-block;
		}

		/* Dark mode for all template content pages */
		body.dark-mode .info-bar {
			background-color: #16213e;
			border-bottom: 1px solid rgba(255, 255, 255, 0.05);
			color: #e0e0e0;
		}
		
		body.dark-mode .info-item {
			color: #b8b8b8;
		}
		
		body.dark-mode .blue-link {
			color: #4da3ff;
		}
		
		body.dark-mode .blue-link:hover {
			color: #6eb5ff;
		}
		
		/* Dark mode for cards, containers and headers */
		body.dark-mode .dashboard-header,
		body.dark-mode .updates-header,
		body.dark-mode .affiliates-header,
		body.dark-mode .terms-header,
		body.dark-mode .ticket-container,
		body.dark-mode .affiliates-info-card,
		body.dark-mode .affiliates-link-card,
		body.dark-mode .affiliates-performance-card,
		body.dark-mode .affiliates-payments-card,
		body.dark-mode .updates-card,
		body.dark-mode .terms-content-card {
			background-color: #16213e;
			box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
		}
		
		/* Dark mode for card headers */
		body.dark-mode .card-header,
		body.dark-mode .ticket-header {
			background-color: rgba(0, 0, 0, 0.2);
			border-bottom: 1px solid rgba(255, 255, 255, 0.05);
		}
		
		body.dark-mode .card-header i,
		body.dark-mode .breadcrumb-nav i,
		body.dark-mode .ticket-header i {
			color: #4da3ff;
		}
		
		/* Dark mode for headings and titles */
		body.dark-mode .affiliates-title h1,
		body.dark-mode .updates-title h1,
		body.dark-mode .terms-title h1,
		body.dark-mode .ticket-title,
		body.dark-mode .current-page,
		body.dark-mode .card-header h2 {
			color: #f0f0f0;
		}
		
		body.dark-mode .affiliates-description,
		body.dark-mode .updates-description,
		body.dark-mode .terms-description {
			color: #b8b8b8;
		}
		
		/* Dark mode for tables */
		body.dark-mode .affiliates-table th,
		body.dark-mode .updates-table th {
			background-color: rgba(0, 0, 0, 0.2);
			border-bottom: 1px solid rgba(255, 255, 255, 0.05);
			color: #f0f0f0;
		}
		
		body.dark-mode .affiliates-table td,
		body.dark-mode .updates-table td {
			border-bottom: 1px solid rgba(255, 255, 255, 0.05);
			color: #e0e0e0;
		}
		
		body.dark-mode .affiliates-table tr:hover,
		body.dark-mode .updates-table tr:hover {
			background-color: rgba(255, 255, 255, 0.03);
		}
		
		/* Dark mode for service updates */
		body.dark-mode .service-id-badge {
			background-color: #0051c4;
		}
		
		body.dark-mode .service-title {
			color: #f0f0f0;
		}
		
		body.dark-mode .date-badge {
			background-color: rgba(33, 119, 255, 0.2);
		}
		
		body.dark-mode .update-content {
			color: #e0e0e0;
		}
		
		/* Dark mode for empty states */
		body.dark-mode .empty-state h3 {
			color: #f0f0f0;
		}
		
		body.dark-mode .empty-state p {
			color: #b8b8b8;
		}
		
		body.dark-mode .empty-icon {
			color: rgba(33, 119, 255, 0.2);
		}
		
		/* Dark mode for search and filters */
		body.dark-mode .search-filter-container .search-input {
			background-color: rgba(255, 255, 255, 0.05);
			border: 1px solid rgba(255, 255, 255, 0.1);
			color: #f0f0f0;
		}
		
		body.dark-mode .search-filter-container .search-input::placeholder {
			color: #7a7a8c;
		}
		
		/* Dark mode for pagination */
		body.dark-mode .page-link {
			background-color: #16213e;
			border: 1px solid rgba(255, 255, 255, 0.05);
			color: #e0e0e0;
		}
		
		body.dark-mode .page-link:hover {
			background-color: rgba(33, 119, 255, 0.1);
			color: #4da3ff;
		}
		
		body.dark-mode .page-item.active .page-link {
			background-color: #1e88e5;
			border-color: #1e88e5;
		}
		
		/* Dark mode for tickets */
		body.dark-mode .message-content {
			background-color: #1a1a2e;
			box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
		}
		
		body.dark-mode .user-message .message-content {
			background-color: rgba(33, 119, 255, 0.1);
		}
		
		body.dark-mode .support-message .message-content {
			background-color: rgba(40, 167, 69, 0.1);
		}
		
		body.dark-mode .message-text,
		body.dark-mode .message-author {
			color: #f0f0f0;
		}
		
		body.dark-mode .message-time {
			color: #b8b8b8;
		}
		
		body.dark-mode .attachment-link {
			background-color: rgba(255, 255, 255, 0.05);
			color: #b8b8b8;
		}
		
		body.dark-mode .attachment-link:hover {
			background-color: rgba(255, 255, 255, 0.1);
			color: #4da3ff;
		}
		
		body.dark-mode .reply-section {
			background-color: #121a2e;
		}
		
		body.dark-mode .reply-header {
			color: #f0f0f0;
		}
		
		body.dark-mode .form-control {
			background-color: rgba(255, 255, 255, 0.05);
			border: 1px solid rgba(255, 255, 255, 0.1);
			color: #f0f0f0;
		}
		
		body.dark-mode .form-control::placeholder {
			color: #7a7a8c;
		}
		
		body.dark-mode .form-control:focus {
			border-color: #1e88e5;
			box-shadow: 0 0 0 4px rgba(30, 136, 229, 0.15);
		}
		
		/* Dark mode for child panels */
		body.dark-mode .well,
		body.dark-mode .well-float {
			background-color: #16213e;
			border: 1px solid rgba(255, 255, 255, 0.05);
			box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
		}
		
		body.dark-mode .table > thead > tr > th {
			background-color: rgba(0, 0, 0, 0.2);
			border-bottom: 2px solid rgba(255, 255, 255, 0.05);
			color: #f0f0f0;
		}
		
		body.dark-mode .table > tbody > tr > td {
			border-top: 1px solid rgba(255, 255, 255, 0.05);
			color: #e0e0e0;
		}
		
		body.dark-mode .table-hover > tbody > tr:hover {
			background-color: rgba(255, 255, 255, 0.03);
		}
		
		body.dark-mode .btn-default {
			background-color: #2c3e50;
			color: #f0f0f0;
			border-color: rgba(255, 255, 255, 0.1);
		}
		
		body.dark-mode .btn-default:hover {
			background-color: #34495e;
			color: #ffffff;
		}
		
		/* Dark mode for alerts */
		body.dark-mode .alert-success {
			background-color: rgba(40, 167, 69, 0.15);
			color: #5cce84;
			border-color: rgba(40, 167, 69, 0.2);
		}
		
		body.dark-mode .alert-danger {
			background-color: rgba(220, 53, 69, 0.15);
			color: #f27d89;
			border-color: rgba(220, 53, 69, 0.2);
		}
		
		body.dark-mode .alert-info {
			background-color: rgba(23, 162, 184, 0.15);
			color: #57cbdb;
			border-color: rgba(23, 162, 184, 0.2);
		}
		
		body.dark-mode .alert-warning {
			background-color: rgba(255, 193, 7, 0.15);
			color: #ffdb5c;
			border-color: rgba(255, 193, 7, 0.2);
		}
		
		/* Dark mode for FAQ and other generic content */
		body.dark-mode .panel {
			background-color: #16213e;
			border: 1px solid rgba(255, 255, 255, 0.05);
			box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
		}
		
		body.dark-mode .panel-heading {
			background-color: rgba(0, 0, 0, 0.2);
			border-bottom: 1px solid rgba(255, 255, 255, 0.05);
		}
		
		body.dark-mode .panel-title {
			color: #f0f0f0;
		}
		
		body.dark-mode .panel-body {
			color: #e0e0e0;
		}
		
		/* Terms and long content pages */
		body.dark-mode .card-content {
			color: #e0e0e0;
		}
		
		body.dark-mode .card-content h1, 
		body.dark-mode .card-content h2, 
		body.dark-mode .card-content h3, 
		body.dark-mode .card-content h4 {
			color: #f0f0f0;
		}
		
		body.dark-mode .card-content h1 {
			border-bottom: 1px solid rgba(255, 255, 255, 0.05);
		}
		
		body.dark-mode .card-content a {
			color: #4da3ff;
		}
		
		body.dark-mode .card-content a:hover {
			color: #6eb5ff;
		}
		
		body.dark-mode .card-content blockquote {
			background-color: rgba(0, 0, 0, 0.2);
			border-left: 4px solid rgba(33, 119, 255, 0.2);
		}
		
		body.dark-mode .rtl-content .card-content blockquote {
			border-left: none;
			border-right: 4px solid rgba(33, 119, 255, 0.2);
		}
		
		body.dark-mode .card-content table th {
			background-color: rgba(0, 0, 0, 0.2);
		}
		
		body.dark-mode .card-content table td {
			border: 1px solid rgba(255, 255, 255, 0.05);
		}
		
		body.dark-mode .card-content table tr:nth-child(even) {
			background-color: rgba(0, 0, 0, 0.1);
		}
		
		/* Dark mode for services, new orders, and order list pages */
		body.dark-mode .service-card,
		body.dark-mode .order-card {
			background-color: #16213e;
			border: 1px solid rgba(255, 255, 255, 0.05);
			box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
		}
		
		body.dark-mode .service-name,
		body.dark-mode .order-number {
			color: #f0f0f0;
		}
		
		body.dark-mode .service-description,
		body.dark-mode .order-details {
			color: #b8b8b8;
		}
		
		body.dark-mode .service-price,
		body.dark-mode .order-price {
			color: #4da3ff;
		}
		
		body.dark-mode .service-category-title,
		body.dark-mode .order-section-title {
			color: #f0f0f0;
			border-bottom: 1px solid rgba(255, 255, 255, 0.05);
		}
		
		body.dark-mode .service-search-box input,
		body.dark-mode .order-search-box input {
			background-color: rgba(255, 255, 255, 0.05);
			border: 1px solid rgba(255, 255, 255, 0.1);
			color: #f0f0f0;
		}
		
		body.dark-mode .service-search-box input::placeholder,
		body.dark-mode .order-search-box input::placeholder {
			color: #7a7a8c;
		}
		
		/* Dark mode for add funds page */
		body.dark-mode .payment-method-card {
			background-color: #16213e;
			border: 1px solid rgba(255, 255, 255, 0.05);
			box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
		}
		
		body.dark-mode .payment-method-title {
			color: #f0f0f0;
		}
		
		body.dark-mode .payment-method-description {
			color: #b8b8b8;
		}
		
		body.dark-mode .payment-amount-input {
			background-color: rgba(255, 255, 255, 0.05);
			border: 1px solid rgba(255, 255, 255, 0.1);
			color: #f0f0f0;
		}
		
		body.dark-mode .payment-amount-input::placeholder {
			color: #7a7a8c;
		}
		
		/* Dark mode for tickets page */
		body.dark-mode .ticket-item {
			background-color: #16213e;
			border: 1px solid rgba(255, 255, 255, 0.05);
			box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
		}
		
		body.dark-mode .ticket-subject {
			color: #f0f0f0;
		}
		
		body.dark-mode .ticket-metadata {
			color: #b8b8b8;
		}
		
		body.dark-mode .status-badge {
			color: #f0f0f0;
		}
		
		/* Dark mode for API page */
		body.dark-mode .api-key-section,
		body.dark-mode .api-endpoint-section {
			background-color: #16213e;
			border: 1px solid rgba(255, 255, 255, 0.05);
			box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
		}
		
		body.dark-mode .api-key {
			background-color: rgba(255, 255, 255, 0.05);
			border: 1px solid rgba(255, 255, 255, 0.1);
			color: #f0f0f0;
		}
		
		body.dark-mode .code-block {
			background-color: #0f1829;
			border: 1px solid rgba(255, 255, 255, 0.1);
			color: #e0e0e0;
		}
		
		body.dark-mode .parameter-name,
		body.dark-mode .endpoint-url {
			color: #4da3ff;
		}
		
		body.dark-mode .parameter-description {
			color: #b8b8b8;
		}
		
		/* Dark mode for FAQ page */
		body.dark-mode .faq-item {
			background-color: #16213e;
			border: 1px solid rgba(255, 255, 255, 0.05);
			box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
		}
		
		body.dark-mode .faq-question {
			color: #f0f0f0;
		}
		
		body.dark-mode .faq-answer {
			color: #e0e0e0;
		}
		</style>

		<!-- Dark Mode Toggle Script -->
		<script>
		// Complete theme system rewrite
		document.addEventListener('DOMContentLoaded', function() {
		  // Elements
		  const themeToggle = document.getElementById('theme-toggle');
		  const moonIcon = document.createElement('i');
		  const sunIcon = document.createElement('i');
		  
		  // Add classes to icons
		  moonIcon.className = 'fa fa-moon';
		  sunIcon.className = 'fa fa-sun';
		  
		  // Ensure proper styling for the theme toggle button (light and dark mode)
		  if (themeToggle) {
			themeToggle.style.display = 'flex';
			themeToggle.style.alignItems = 'center';
			themeToggle.style.justifyContent = 'center';
			themeToggle.style.borderRadius = '50%';
			themeToggle.style.width = '40px';
			themeToggle.style.height = '40px';
			themeToggle.style.border = '1px solid rgba(0, 0, 0, 0.1)';
			themeToggle.style.cursor = 'pointer';
			
			// Set both icons in button (one will be hidden via CSS)
			themeToggle.innerHTML = '';
			themeToggle.appendChild(moonIcon);
			themeToggle.appendChild(sunIcon);
			
			// Apply appropriate visibility based on current theme
			const isDarkMode = document.body.classList.contains('dark-mode');
			if (isDarkMode) {
			  moonIcon.style.display = 'none';
			  sunIcon.style.display = 'flex';
			} else {
			  moonIcon.style.display = 'flex';
			  sunIcon.style.display = 'none';
			}
			
			// Add click handler
			themeToggle.addEventListener('click', function() {
			  console.log('Theme toggle clicked');
			  const isDark = document.body.classList.contains('dark-mode');
			  
			  if (isDark) {
				// Switch to light
				document.body.classList.remove('dark-mode');
				document.documentElement.classList.remove('dark-theme');
				localStorage.setItem('theme', 'light-mode');
				moonIcon.style.display = 'flex';
				sunIcon.style.display = 'none';
			  } else {
				// Switch to dark
				document.body.classList.add('dark-mode');
				document.documentElement.classList.add('dark-theme');
				localStorage.setItem('theme', 'dark-mode');
				moonIcon.style.display = 'none';
				sunIcon.style.display = 'flex';
			  }
			});
		  }
		  
		  // Ensure body is visible (in case it was hidden to prevent flash)
		  document.body.style.opacity = '1';
		  document.documentElement.classList.remove('theme-transitioning');
		});
		</script>

		<!-- Immediate theme application script in head -->
		<script>
		(function() {
		  try {
			var theme = localStorage.getItem('theme');
			if (theme === 'dark-mode') {
			  document.documentElement.classList.add('dark-theme');
			  document.documentElement.classList.add('theme-transitioning');
			  document.body.classList.add('dark-mode');
			}
		  } catch (e) {
			console.error('Theme init error:', e);
		  }
		})();
		</script>

		<!-- Fix the auth header dropdown items in dark mode -->
		<style>
		body.dark-mode .auth-dropdown .dropdown-content {
		  background-color: #1c2940 !important;
		  border: 1px solid rgba(255, 255, 255, 0.1) !important;
		}

		body.dark-mode .auth-dropdown .dropdown-content a {
		  color: #e0e0e0 !important;
		}

		body.dark-mode .auth-dropdown .dropdown-content a:hover {
		  background-color: rgba(77, 163, 255, 0.1) !important;
		}

		body.dark-mode .dropdown-content a {
		  color: #e0e0e0 !important;
		}

		body.dark-mode .dropdown-content a i {
		  color: #4da3ff !important;
		}

		body.dark-mode .auth-icon-link {
		  color: #e0e0e0 !important;
		}

		body.dark-mode .auth-icon-link:hover {
		  color: #4da3ff !important;
		}

		body.dark-mode .auth-header {
		  background-color: #16213e !important; 
		  border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
		}

		body.dark-mode #userDropdownContent {
		  background-color: #1c2940 !important;
		  color: #e0e0e0 !important;
		  border: 1px solid rgba(255, 255, 255, 0.1) !important;
		  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3) !important;
		}

		body.dark-mode #userDropdownContent a {
		  color: #e0e0e0 !important;
		}

		body.dark-mode #userDropdownContent a:hover {
		  background-color: rgba(77, 163, 255, 0.1) !important;
		}

		body.dark-mode .telegram-dropdown {
		  background-color: #1c2940 !important;
		  color: #e0e0e0 !important;
		  border: 1px solid rgba(255, 255, 255, 0.1) !important;
		  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3) !important;
		}

		body.dark-mode .telegram-dropdown a {
		  color: #e0e0e0 !important;
		}

		body.dark-mode .telegram-dropdown a:hover {
		  background-color: rgba(77, 163, 255, 0.1) !important;
		}

		/* Ensure dark mode is applied to the main content area */
		body.dark-mode #mainContent {
		  background-color: #1a1a2e !important;
		}
		</style>

		<!-- Fix header icons in dark mode -->
		<style>
		body.dark-mode .auth-header {
		  background-color: #16213e !important;
		}

		body.dark-mode .auth-icon-link {
		  background-color: rgba(255, 255, 255, 0.1) !important;
		  color: #e0e0e0 !important;
		  border-radius: 50%;
		  width: 40px;
		  height: 40px;
		  display: flex;
		  align-items: center;
		  justify-content: center;
		  transition: all 0.3s ease;
		}

		body.dark-mode .auth-icon-link:hover {
		  background-color: rgba(77, 163, 255, 0.2) !important;
		  color: #4da3ff !important;
		}

		body.dark-mode .theme-toggle-btn {
		  background-color: rgba(255, 255, 255, 0.1) !important;
		  color: #e0e0e0 !important;
		  border-radius: 50%;
		  width: 40px;
		  height: 40px;
		  display: flex;
		  align-items: center;
		  justify-content: center;
		  transition: all 0.3s ease;
		}

		body.dark-mode .theme-toggle-btn:hover {
		  background-color: rgba(77, 163, 255, 0.2) !important;
		  color: #4da3ff !important;
		}

		/* Fix specifically for button text visibility */
		body.dark-mode .auth-actions a i,
		body.dark-mode .auth-actions button i {
		  color: #e0e0e0 !important;
		}

		body.dark-mode .auth-actions a:hover i,
		body.dark-mode .auth-actions button:hover i {
		  color: #4da3ff !important;
		}

		/* Fix Auth Header logo */
		body.dark-mode .auth-logo img {
		  filter: brightness(1.2) !important;
		}

		/* Add a subtle border to make the circles more visible */
		body.dark-mode .auth-icon-link,
		body.dark-mode .theme-toggle-btn {
		  border: 1px solid rgba(255, 255, 255, 0.15) !important;
		}

		/* Ensure there's appropriate spacing between the header elements */
		.auth-actions {
		  display: flex;
		  align-items: center;
		  gap: 15px;
		}

		.auth-dropdown {
		  position: relative;
		}

		/* Make sure gear icon is centered correctly */
		body.dark-mode .fa-cog,
		body.dark-mode .fa-user,
		body.dark-mode .fa-paper-plane,
		body.dark-mode .fa-moon,
		body.dark-mode .fa-sun {
		  font-size: 18px !important;
		  width: auto !important;
		  height: auto !important;
		  display: flex !important;
		  align-items: center !important;
		  justify-content: center !important;
		}
		</style>
	</body>
</html>