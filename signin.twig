<!-- Just the Signin Section from Khmer SMM -->
<div class="khmer-signin-section">
  <div class="container">
    <div class="row align-items-center">
      <!-- Left Side - Content & Form -->
      <div class="col-lg-6 col-md-6">
        <h4 class="khmer-subtitle">Chhean SMM -Top-Rated SMM Panel</h4>
        <h1 class="khmer-title">
          <span class="blue-text">#1 Cheapest SMM </span><span class="black-text">Panel in Cambodia</span>
        </h1>
        <p class="khmer-description">
          Experience unparalleled growth on social media with our expert SMM services.
        </p>

        <!-- Signin Form -->
        <div class="khmer-form-box">
          <form method="post" action="{{ page['url'] }}" class="khmer-form{% if site['rtl'] %} rtl-form{% endif %}">

            <!-- Alerts -->
            {% if error %}
              <div class="alert alert-danger">{{ errorMessage }}</div>
            {% endif %}
            {% if success %}
              <div class="alert alert-success">{{ successMessage }}</div>
            {% endif %}

            <!-- Username Input -->
            <div class="khmer-input-group">
              <img src="https://storage.perfectcdn.com/n675sa/fsr9xi1bs4bsf96k.png" class="input-icon" alt="Username">
              <input type="text" class="khmer-input" name="LoginForm[username]" placeholder="" required>
            </div>

            <!-- Password Input -->
            <div class="khmer-input-group">
              <img src="https://storage.perfectcdn.com/n675sa/be0gwra8am77m3k5.png" class="input-icon" alt="Password">
              <input type="password" class="khmer-input" name="LoginForm[password]" placeholder="•••" required>
              <span class="password-toggle">👁</span>
            </div>

            <!-- Remember Me & Forgot Password -->
            <div class="khmer-options">
              <label class="khmer-checkbox">
                <input type="checkbox" name="LoginForm[remember]" value="1">
                Remember me
              </label>
              {% if site['forgotPassword'] %}
                <a href="/resetpassword" class="khmer-forgot">Forgot password?</a>
              {% endif %}
            </div>

            <!-- Captcha -->
            {% if captcha %}
              <div class="khmer-captcha">{{ captchaCode }}</div>
            {% endif %}

            <!-- Buttons Row -->
            <input type="hidden" name="_csrf" value="{{ csrftoken }}">
            <div class="khmer-buttons-row">
              <button type="submit" class="khmer-signin-btn">Sign in</button>
              {% if googleSignIn %}
                <div class="khmer-google-inline">
                  <div id="g_id_onload" data-client_id="{{ googleClientId }}" data-login_uri="{{ googleSignInRedirectUrl }}" data-auto_prompt="false" data-_csrf="{{ csrftoken }}"></div>
                  <div class="g_id_signin" data-type="standard" data-size="large" data-theme="outline" data-text="sign_in_with" data-shape="rectangular" data-logo_alignment="left"></div>
                </div>
              {% endif %}
            </div>

            <!-- Sign Up Link -->
            {% if registration %}
              <div class="khmer-signup">
                Do not have an account? <a href="/signup">Sign up</a>
              </div>
            {% endif %}

          </form>
        </div>
      </div>

      <!-- Right Side - Illustration -->
      <div class="col-lg-6 col-md-6">
        <div class="khmer-illustration">
          <img src="https://storage.perfectcdn.com/n675sa/yglatn863qqoo20e.jpg" alt="SMM Illustration" class="illustration-image">
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Statistics Section -->
<div class="khmer-stats-section">
  <div class="container">
    <div class="row">
      <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="khmer-stat-card">
          <div class="stat-icon">
            <img src="https://storage.perfectcdn.com/n675sa/lxnmb383a5l0xzp8.png" alt="Orders Completed">
          </div>
          <div class="stat-content">
            <h3 class="stat-number">{{ totals['ordersCompleted']|default('2377776') }}</h3>
            <p class="stat-label">Order Completed</p>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="khmer-stat-card">
          <div class="stat-icon">
            <img src="https://storage.perfectcdn.com/n675sa/1a4ra8dbxn4c10dz.png" alt="Active Services">
          </div>
          <div class="stat-content">
            <h3 class="stat-number">{{ totals['servicesAll']|default('4784') }}</h3>
            <p class="stat-label">Active Services</p>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="khmer-stat-card">
          <div class="stat-icon">
            <img src="https://storage.perfectcdn.com/n675sa/m5tsihrningiznmy.png" alt="Active Users">
          </div>
          <div class="stat-content">
            <h3 class="stat-number">{{ totals['usersActive']|default('29067') }}</h3>
            <p class="stat-label">Active Users</p>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="khmer-stat-card">
          <div class="stat-icon">
            <img src="https://storage.perfectcdn.com/n675sa/ib40dsxmgltmu2cp.png" alt="Cambodia Rank">
          </div>
          <div class="stat-content">
            <h3 class="stat-number">#1</h3>
            <p class="stat-label">Cambodia Rank</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Services Section -->
<div class="khmer-services-section">
  <div class="container">
    <div class="row">
      <div class="col-lg-12">
        <div class="services-header">
          <h4 class="services-subtitle">Our Services</h4>
          <h2 class="services-title">Diverse <span class="blue-text">SMM Panels</span> for Every Need</h2>
          <p class="services-description">
            Explore our comprehensive range of SMM panels, designed to cater to every aspect of your social media needs. From boosting your Instagram
            followers to enhancing your LinkedIn network, our specialized services are tailored to ensure maximum impact and growth for your online
            presence. With Khmer SMM, experience the power of targeted and effective social media marketing.
          </p>
        </div>

        <!-- Social Media Icons Grid -->
        <div class="social-icons-container">
          <div class="social-icons-row">
            <div class="social-icon-button" data-platform="facebook">
              <i class="fab fa-facebook-f"></i>
              <span>Facebook</span>
            </div>

            <div class="social-icon-button" data-platform="instagram">
              <i class="fab fa-instagram"></i>
              <span>Instagram</span>
            </div>

            <div class="social-icon-button" data-platform="youtube">
              <i class="fab fa-youtube"></i>
              <span>Youtube</span>
            </div>

            <div class="social-icon-button" data-platform="tiktok">
              <i class="fab fa-tiktok"></i>
              <span>TikTok</span>
            </div>

            <div class="social-icon-button" data-platform="linkedin">
              <i class="fab fa-linkedin-in"></i>
              <span>Linkedin</span>
            </div>

            <div class="social-icon-button" data-platform="telegram">
              <i class="fab fa-telegram-plane"></i>
              <span>Telegram</span>
            </div>
          </div>

          <div class="social-icons-row">
            <div class="social-icon-button" data-platform="discord">
              <i class="fab fa-discord"></i>
              <span>Discord</span>
            </div>

            <div class="social-icon-button" data-platform="twitter">
              <i class="fab fa-twitter"></i>
              <span>X-Twitter</span>
            </div>

            <div class="social-icon-button" data-platform="pinterest">
              <i class="fab fa-pinterest-p"></i>
              <span>Pinterest</span>
            </div>

            <div class="social-icon-button" data-platform="twitch">
              <i class="fab fa-twitch"></i>
              <span>Twitch</span>
            </div>

            <div class="social-icon-button" data-platform="soundcloud">
              <i class="fab fa-soundcloud"></i>
              <span>Sound Cloud</span>
            </div>

            <div class="social-icon-button" data-platform="webtraffic">
              <i class="fas fa-globe"></i>
              <span>Web Traffic</span>
            </div>
          </div>
        </div>

        <!-- Featured Service Panel -->
        <div class="featured-service" id="featured-service">
          <div class="row align-items-center">
            <div class="col-lg-6">
              <h3 class="featured-title" id="service-title">Facebook SMM Panel</h3>
              <p class="featured-description" id="service-description">
                Discover the best SMM panel for Facebook to skyrocket your page's engagement and
                reach. Experience affordable, impactful growth with our expert Facebook marketing
                strategies.
              </p>
            </div>
            <div class="col-lg-6">
              <div class="featured-logo" id="service-logo">
                <i class="fab fa-facebook-f" id="service-icon"></i>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </section>

  <!-- Transform Your Social Media Section - Full Width -->
  <section class="transform-section-fullwidth">
    <div class="container">
      <div class="row align-items-center">
        <div class="col-lg-6">
          <h2 class="transform-title">Ready to <span class="highlight-text">Transform</span> Your Social Media Presence?</h2>
          <p class="transform-description">
            Boost your online influence effortlessly. Discover the power of our specialized SMM panels tailored for success. Affordable, impactful, and ready to launch your brand to new heights.
          </p>
          <button class="transform-btn" onclick="window.location.href='/signup'">
            Elevate Your Social Game Now
          </button>
        </div>
        <div class="col-lg-6">
          <div class="transform-image">
            <img src="https://storage.perfectcdn.com/n675sa/ohc543f5bjzjhro5.png" alt="Transform Your Social Media" class="img-fluid">
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- How It Works Section -->
  <section class="how-it-works-section">
    <div class="container">
      <div class="text-center mb-5">
        <p class="section-subtitle">How It Works?</p>
        <h2 class="section-title">Start Your <span class="highlight-text">Success Story</span></h2>
        <h3 class="section-subtitle-main">with Chhean SMM in Four Easy Steps</h3>
        <p class="section-description">
          Embark on a journey to social media success with Chhean SMM in just four straightforward steps. From easy sign-up to witnessing your brand's growth, our process is designed for ease and effectiveness. Let us guide you through each step, ensuring a hassle-free path to enhancing your online presence.
        </p>
      </div>

      <div class="row">
        <!-- Step 1 -->
        <div class="col-lg-3 col-md-6 mb-4">
          <div class="step-card">
            <div class="step-number">
              <span class="step-num">01</span>
              <span class="step-label">Step</span>
            </div>
            <h4 class="step-title">Easy Sign-Up</h4>
            <p class="step-description">
              Kickstart your adventure by creating an account with us. Quick, straightforward, and the first step towards a thrilling social media journey.
            </p>
          </div>
        </div>

        <!-- Step 2 -->
        <div class="col-lg-3 col-md-6 mb-4">
          <div class="step-card">
            <div class="step-number">
              <span class="step-num">02</span>
              <span class="step-label">Step</span>
            </div>
            <h4 class="step-title">Customize Your Plan</h4>
            <p class="step-description">
              Dive into our array of SMM services and select the one that suits your needs. Tailor it perfectly, ensuring it aligns seamlessly with your unique brand goals.
            </p>
          </div>
        </div>

        <!-- Step 3 -->
        <div class="col-lg-3 col-md-6 mb-4">
          <div class="step-card">
            <div class="step-number">
              <span class="step-num">03</span>
              <span class="step-label">Step</span>
            </div>
            <h4 class="step-title">Make a Secure Payment</h4>
            <p class="step-description">
              Proceed with confidence using our secure payment gateway. Your safety and privacy are paramount as you take the vital step.
            </p>
          </div>
        </div>

        <!-- Step 4 -->
        <div class="col-lg-3 col-md-6 mb-4">
          <div class="step-card">
            <div class="step-number">
              <span class="step-num">04</span>
              <span class="step-label">Step</span>
            </div>
            <h4 class="step-title">Watch Your Growth & Enjoy</h4>
            <p class="step-description">
              Now, sitting back and watching your social media presence flourish is exciting. Revel in the growth and engagement, knowing you've made an intelligent choice with Chhean SMM.
            </p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- About Chhean SMM Section -->
  <section class="about-section">
    <div class="about-container">
      <div class="container">
        <div class="row align-items-center">
          <div class="col-lg-6">
            <p class="about-subtitle">About Chhean SMM</p>
            <h2 class="about-title">Your Premier SMM Partner</h2>
            <p class="about-description">
              At Chhean SMM, we specialize in unlocking the full potential of your social media platforms. As Cambodia's #1 cheapest and most trusted SMM panel provider, we offer a comprehensive suite of services tailored to your brand's unique needs. From Facebook and Instagram to TikTok and LinkedIn, we aim to elevate your online presence with cost-effective, result-oriented strategies.
            </p>
            <p class="about-description">
              Our commitment to quality and affordability has made us the go-to choice for businesses seeking impactful digital growth. Since our establishment in 2021, we have been at the forefront of digital marketing innovation in Cambodia and Southeast Asia. Join us and experience the transformative power of expert social media marketing.
            </p>
            <button class="about-btn">More About Us</button>
          </div>
          <div class="col-lg-6">
            <div class="about-image">
              <img src="https://storage.perfectcdn.com/n675sa/wuq2fag4cn8jopfb.png" alt="About Chhean SMM" class="img-fluid">
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Why Choose Section -->
  <section class="why-choose-section">
    <div class="why-choose-container">
      <div class="container">
        <div class="row">
          <div class="col-12 text-center mb-5">
            <h6 class="why-choose-subtitle">Why Choose Chhean SMM?</h6>
            <h2 class="why-choose-title">Unveiling the Reasons Behind<br>Our <span class="text-primary">Success</span></h2>
            <p class="why-choose-description">When elevating your social media presence, choosing the right partner is crucial. At Chhean SMM, we stand out as the best SMM panel provider in Cambodia, offering an array of compelling reasons to select our services. Here's why we're the top choice for businesses and individuals alike:</p>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-4 col-md-6 mb-4">
            <div class="why-choose-card">
              <div class="why-choose-icon">
                <img src="https://storage.perfectcdn.com/n675sa/xnb1a02floaamj9a.png" alt="Proven Expertise">
              </div>
              <h5 class="why-choose-card-title">Proven Expertise</h5>
              <p class="why-choose-card-text">With years of experience, Chhean SMM is renowned as the top-rated SMM panel in Cambodia, consistently delivering exceptional results.</p>
            </div>
          </div>
          <div class="col-lg-4 col-md-6 mb-4">
            <div class="why-choose-card">
              <div class="why-choose-icon">
                <img src="https://storage.perfectcdn.com/n675sa/153plckrpswxh1nc.png" alt="Wide Range Of Services">
              </div>
              <h5 class="why-choose-card-title">Wide Range Of Services</h5>
              <p class="why-choose-card-text">From Facebook to TikTok, our diverse services cater to all your social media needs, making us a one-stop solution for digital growth.</p>
            </div>
          </div>
          <div class="col-lg-4 col-md-6 mb-4">
            <div class="why-choose-card">
              <div class="why-choose-icon">
                <img src="https://storage.perfectcdn.com/n675sa/z41n99gye5sz7aw7.png" alt="Affordable Pricing">
              </div>
              <h5 class="why-choose-card-title">Affordable Pricing</h5>
              <p class="why-choose-card-text">As a cheap SMM panel provider, we ensure that our high-quality services are accessible to all, balancing affordability with excellence.</p>
            </div>
          </div>
          <div class="col-lg-4 col-md-6 mb-4">
            <div class="why-choose-card">
              <div class="why-choose-icon">
                <img src="https://storage.perfectcdn.com/n675sa/2rtjzstpktr08ra1.png" alt="Customer-Centric Approach">
              </div>
              <h5 class="why-choose-card-title">Customer-Centric Approach</h5>
              <p class="why-choose-card-text">We prioritize your needs, tailoring our services to meet your specific goals and ensuring a personalized experience.</p>
            </div>
          </div>
          <div class="col-lg-4 col-md-6 mb-4">
            <div class="why-choose-card">
              <div class="why-choose-icon">
                <img src="https://storage.perfectcdn.com/n675sa/gmflgrtftnuklgft.png" alt="Rapid Results">
              </div>
              <h5 class="why-choose-card-title">Rapid Results</h5>
              <p class="why-choose-card-text">We prioritize your needs, tailoring our services to meet your specific goals and ensuring a personalized experience.</p>
            </div>
          </div>
          <div class="col-lg-4 col-md-6 mb-4">
            <div class="why-choose-card">
              <div class="why-choose-icon">
                <img src="https://storage.perfectcdn.com/n675sa/pxbsdgs47l0ni9s1.png" alt="Unmatched Support">
              </div>
              <h5 class="why-choose-card-title">Unmatched Support</h5>
              <p class="why-choose-card-text">We prioritize your needs, tailoring our services to meet your specific goals and ensuring a personalized experience.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Testimonials Section -->
  <section class="testimonials-section">
    <div class="testimonials-container">
      <div class="container">
        <div class="row align-items-center">
          <div class="col-lg-6">
            <div class="testimonials-image">
              <img src="https://storage.perfectcdn.com/n675sa/yglatn863qqoo20e.jpg" alt="Happy Customers" class="img-fluid">
            </div>
          </div>
          <div class="col-lg-6">
            <div class="testimonials-content">
              <h6 class="testimonials-subtitle">Hear from Our <span class="text-primary">Happy Clients</span> - Real Stories, Real Success</h6>
              <p class="testimonials-description">Nothing speaks louder than the voices of satisfied customers. At Chhean SMM, we pride ourselves on the positive impacts we've made on our clients. Here are genuine reviews from some of our valued customers in Cambodia, sharing their experiences and their success with our SMM services.</p>

              <div class="testimonial-slider">
                <div class="testimonial-track">
                  <div class="testimonial-item" data-testimonial="1">
                    <div class="testimonial-quote">
                      <i class="fas fa-quote-left quote-icon"></i>
                      <p class="testimonial-text">As a small business owner in Phnom Penh, I was struggling to make a mark on social media. That's when I turned to Chhean SMM. Their Facebook SMM panel boosted my page's engagement and significantly increased my sales. It's been a game-changer for my business!</p>
                    </div>
                    <div class="testimonial-rating">
                      <i class="fas fa-star"></i>
                      <i class="fas fa-star"></i>
                      <i class="fas fa-star"></i>
                      <i class="fas fa-star"></i>
                      <i class="fas fa-star"></i>
                    </div>
                    <div class="testimonial-author">
                      <h6>Sophea Chan</h6>
                      <span>Restaurant Owner, Phnom Penh</span>
                    </div>
                  </div>

                  <div class="testimonial-item" data-testimonial="2">
                    <div class="testimonial-quote">
                      <i class="fas fa-quote-left quote-icon"></i>
                      <p class="testimonial-text">I run a fashion boutique in Siem Reap and needed to increase my Instagram followers. Chhean SMM delivered exactly what they promised. My follower count grew organically, and I've seen a 300% increase in online orders!</p>
                    </div>
                    <div class="testimonial-rating">
                      <i class="fas fa-star"></i>
                      <i class="fas fa-star"></i>
                      <i class="fas fa-star"></i>
                      <i class="fas fa-star"></i>
                      <i class="fas fa-star"></i>
                    </div>
                    <div class="testimonial-author">
                      <h6>Dara Pich</h6>
                      <span>Fashion Boutique Owner, Siem Reap</span>
                    </div>
                  </div>

                  <div class="testimonial-item" data-testimonial="3">
                    <div class="testimonial-quote">
                      <i class="fas fa-quote-left quote-icon"></i>
                      <p class="testimonial-text">As a content creator from Battambang, I needed help growing my TikTok presence. Chhean SMM's services helped me reach 50K followers in just 2 months. Their support team is always responsive and professional!</p>
                    </div>
                    <div class="testimonial-rating">
                      <i class="fas fa-star"></i>
                      <i class="fas fa-star"></i>
                      <i class="fas fa-star"></i>
                      <i class="fas fa-star"></i>
                      <i class="fas fa-star"></i>
                    </div>
                    <div class="testimonial-author">
                      <h6>Pisach Lim</h6>
                      <span>Content Creator, Battambang</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="testimonial-navigation">
                <button class="testimonial-nav-btn prev-btn" onclick="changeTestimonial(-1)">
                  <i class="fas fa-chevron-left"></i>
                </button>
                <button class="testimonial-nav-btn next-btn" onclick="changeTestimonial(1)">
                  <i class="fas fa-chevron-right"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Who Can Benefit Section -->
  <section class="who-benefit-section">
    <div class="who-benefit-container">
      <div class="container">
        <div class="row">
          <div class="col-12 text-center mb-5">
            <h2 class="who-benefit-title">Discover Who Can Benefit from SMM<br>Panels – <span class="text-primary">Is It You?</span></h2>
            <p class="who-benefit-description">In today's digital-first world, the power of social media is undeniable. Whether you're a budding entrepreneur, a thriving business, or an influencer aiming to broaden your reach, understanding the role of SMM (Social Media Marketing) panels can be a game-changer. But who needs an SMM panel, and how can it revolutionize your digital strategy? Let's dive in.</p>
          </div>
        </div>
        <!-- Masonry Layout to match reference exactly -->
        <div class="benefit-masonry-container">
          <!-- Card 1: Small Business (Blue, Tall) -->
          <div class="benefit-card blue-card">
            <div class="benefit-content">
              <h5 class="benefit-card-title">Small Business Owners and Startups</h5>
              <p class="benefit-card-text">Small businesses need to make their mark in Cambodia, where the startup ecosystem is booming. With over 60 million Facebook users in the country as of 2023, the opportunity to reach a vast audience is immense. An SMM panel can help these businesses target their local audience effectively, ensuring higher engagement and conversion rates.</p>
            </div>
            <div class="benefit-image">
              <img src="https://storage.perfectcdn.com/n675sa/igj2txu7qlhtq5c1.webp" alt="Small Business Owners">
            </div>
          </div>

          <!-- Card 2: Influencers (Purple, Tall) -->
          <div class="benefit-card purple-card">
            <div class="benefit-content">
              <h5 class="benefit-card-title">Influencers and Content Creators</h5>
              <p class="benefit-card-text">Small businesses need to make their mark in Cambodia, where the startup ecosystem is booming. With over 60 million Facebook users in the country as of 2023, the opportunity to reach a vast audience is immense. An SMM panel can help these businesses target their local audience effectively, ensuring higher engagement and conversion rates.</p>
            </div>
            <div class="benefit-image">
              <img src="https://storage.perfectcdn.com/n675sa/cj9pked6wxsowbxh.webp" alt="Influencers and Content Creators">
            </div>
          </div>

          <!-- Card 3: E-commerce (Light, Medium) -->
          <div class="benefit-card light-card">
            <div class="benefit-content">
              <h5 class="benefit-card-title">E-commerce Platforms</h5>
              <p class="benefit-card-text">The e-commerce sector in Cambodia has seen exponential growth, with a 70% increase in online shopping reported recently. An SMM panel can drive targeted traffic to these e-commerce platforms, increasing brand visibility and sales.</p>
            </div>
            <div class="benefit-image">
              <img src="https://storage.perfectcdn.com/n675sa/lsw3eepgsogtxpl9.webp" alt="E-commerce Platforms">
            </div>
          </div>

          <!-- Card 4: Corporate (Orange, Medium) -->
          <div class="benefit-card orange-card">
            <div class="benefit-content">
              <h5 class="benefit-card-title">Corporates and Large Enterprises</h5>
              <p class="benefit-card-text">For larger corporations, maintaining a strong social media presence is essential for brand image and customer engagement. SMM panels offer a way to manage multiple social media accounts efficiently, ensuring consistent and impactful communication.</p>
            </div>
            <div class="benefit-image">
              <img src="https://storage.perfectcdn.com/n675sa/5wsls1wxqe5la6o8.webp" alt="Corporates and Large Enterprises">
            </div>
          </div>

          <!-- Card 5: Event Organizers (Blue, Medium) -->
          <div class="benefit-card blue-card">
            <div class="benefit-content">
              <h5 class="benefit-card-title">Event Organizers</h5>
              <p class="benefit-card-text">With events and festivals being a core part of Cambodian culture, event organizers can leverage SMM panels to boost event awareness and attendance. Targeted campaigns can effectively reach potential attendees, making every event a success.</p>
            </div>
            <div class="benefit-image">
              <img src="https://storage.perfectcdn.com/n675sa/ac7yrnyxcsxn15gw.webp" alt="Event Organizers">
            </div>
          </div>

          <!-- Card 6: Education (Purple, Medium) -->
          <div class="benefit-card purple-card">
            <div class="benefit-content">
              <h5 class="benefit-card-title">Educational Institutions and NGO's</h5>
              <p class="benefit-card-text">Educational bodies and NGOs can use SMM panels to spread awareness about their causes, programs, and initiatives in a country with a growing youth population eager for education and social change. Social media is a powerful tool to disseminate information and rally support.</p>
            </div>
            <div class="benefit-image">
              <img src="https://storage.perfectcdn.com/n675sa/z5gcl3qs5ciq48ki.webp" alt="Educational Institutions and NGOs">
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- FAQ Section -->
  <section class="faq-section">
    <div class="faq-container">
      <div class="container">
        <div class="text-center mb-5">
          <h2 class="faq-title">FAQ</h2>
          <p class="faq-description">Get quick answers to your most pressing questions about Chhean SMM and our SMM services. This FAQ section covers everything from our service range to payment methods, ensuring you have all the information you need for a seamless experience.</p>
        </div>

        <div class="faq-grid">
          <div class="faq-column faq-column-left">
            <div class="faq-item">
              <div class="faq-question" onclick="toggleFAQ(this)">
                <span>What is an SMM Panel?</span>
                <i class="fas fa-chevron-down faq-icon"></i>
              </div>
              <div class="faq-answer">
                <div class="faq-content">
                  An SMM (Social Media Marketing) panel is a web-based platform that provides social media marketing services like followers, likes, views, and engagement for various social media platforms including Facebook, Instagram, YouTube, TikTok, and more.
                </div>
              </div>
            </div>

            <div class="faq-item">
              <div class="faq-question" onclick="toggleFAQ(this)">
                <span>How does Chhean SMM's SMM Panel work?</span>
                <i class="fas fa-chevron-down faq-icon"></i>
              </div>
              <div class="faq-answer">
                <div class="faq-content">
                  Simply register an account, add funds to your balance, select the service you want, provide the link to your social media post or profile, and place your order. Our system will start processing your order immediately.
                </div>
              </div>
            </div>

            <div class="faq-item">
              <div class="faq-question" onclick="toggleFAQ(this)">
                <span>Is using an SMM Panel safe for my social media accounts?</span>
                <i class="fas fa-chevron-down faq-icon"></i>
              </div>
              <div class="faq-answer">
                <div class="faq-content">
                  Yes, our services are completely safe. We use gradual delivery methods and high-quality accounts to ensure your social media profiles remain secure and comply with platform guidelines.
                </div>
              </div>
            </div>

            <div class="faq-item">
              <div class="faq-question" onclick="toggleFAQ(this)">
                <span>What platforms does Chhean SMM offer services for?</span>
                <i class="fas fa-chevron-down faq-icon"></i>
              </div>
              <div class="faq-answer">
                <div class="faq-content">
                  We provide services for all major social media platforms including Facebook, Instagram, YouTube, TikTok, Twitter, LinkedIn, Spotify, SoundCloud, and many more.
                </div>
              </div>
            </div>

            <div class="faq-item">
              <div class="faq-question" onclick="toggleFAQ(this)">
                <span>Can I track the progress of my orders?</span>
                <i class="fas fa-chevron-down faq-icon"></i>
              </div>
              <div class="faq-answer">
                <div class="faq-content">
                  Yes, you can track all your orders in real-time through your dashboard. You'll see the status, progress, and completion details of each order.
                </div>
              </div>
            </div>

            <div class="faq-item">
              <div class="faq-question" onclick="toggleFAQ(this)">
                <span>Can I use Chhean SMM for personal and business accounts?</span>
                <i class="fas fa-chevron-down faq-icon"></i>
              </div>
              <div class="faq-answer">
                <div class="faq-content">
                  Absolutely! Our services work perfectly for both personal profiles and business accounts. We offer tailored solutions for individual users, influencers, small businesses, and large enterprises.
                </div>
              </div>
            </div>
          </div>

          <div class="faq-column faq-column-right">
            <div class="faq-item">
              <div class="faq-question" onclick="toggleFAQ(this)">
                <span>Are the followers or likes provided by Chhean SMM real?</span>
                <i class="fas fa-chevron-down faq-icon"></i>
              </div>
              <div class="faq-answer">
                <div class="faq-content">
                  Yes, we provide high-quality, real followers and engagement from active accounts. Our services are designed to help you build authentic social media presence with genuine interactions.
                </div>
              </div>
            </div>

            <div class="faq-item">
              <div class="faq-question" onclick="toggleFAQ(this)">
                <span>What payment methods are accepted by Chhean SMM?</span>
                <i class="fas fa-chevron-down faq-icon"></i>
              </div>
              <div class="faq-answer">
                <div class="faq-content">
                  We accept Bakong, ABA Bank, and Binance Pay for your convenience. All transactions are secure and processed quickly to ensure seamless payment experience.
                </div>
              </div>
            </div>

            <div class="faq-item">
              <div class="faq-question" onclick="toggleFAQ(this)">
                <span>What should I do if I face any issues with my order?</span>
                <i class="fas fa-chevron-down faq-icon"></i>
              </div>
              <div class="faq-answer">
                <div class="faq-content">
                  If you encounter any issues, please contact our 24/7 customer support team. You can reach us through our support ticket system or directly via Telegram at <a href="https://t.me/chheansmm_support" target="_blank" class="telegram-link">@chheansmm_support</a>.
                </div>
              </div>
            </div>

            <div class="faq-item">
              <div class="faq-question" onclick="toggleFAQ(this)">
                <span>How long does it take to see results after placing an order?</span>
                <i class="fas fa-chevron-down faq-icon"></i>
              </div>
              <div class="faq-answer">
                <div class="faq-content">
                  Delivery times vary depending on the service. Most orders start within 0-1 hours and complete within 24-72 hours. Larger orders may take longer for natural, gradual delivery.
                </div>
              </div>
            </div>

            <div class="faq-item">
              <div class="faq-question" onclick="toggleFAQ(this)">
                <span>Does Chhean SMM offer customized packages?</span>
                <i class="fas fa-chevron-down faq-icon"></i>
              </div>
              <div class="faq-answer">
                <div class="faq-content">
                  Yes, we offer customized packages for businesses and influencers with specific needs. Contact us via Telegram <a href="https://t.me/chheansmm_support" target="_blank" class="telegram-link">@chheansmm_support</a> to discuss your requirements.
                </div>
              </div>
            </div>

            <div class="faq-item">
              <div class="faq-question" onclick="toggleFAQ(this)">
                <span>Can I cancel or modify my order once placed?</span>
                <i class="fas fa-chevron-down faq-icon"></i>
              </div>
              <div class="faq-answer">
                <div class="faq-content">
                  Orders can be cancelled or modified only if they haven't started processing yet. Once processing begins, modifications may not be possible. Contact our support team immediately if you need changes.
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="text-center mt-5">
          <p class="faq-contact">Still have questions? Contact us on Telegram: <a href="https://t.me/chheansmm_support" target="_blank" class="telegram-contact">@chheansmm_support</a></p>
        </div>
      </div>
    </div>
  </section>



  <section class="signin-section">
    <div class="container">
      <div class="row justify-content-center">
      </div>
    </div>
  </div>
</div>

<!-- Additional Content -->
{% if authText %}
  <div class="container">
    <div class="row">
      <div class="col-md-8 col-md-offset-2">
        <div class="well{% if site['rtl'] %} rtl-content{% endif %}">{{ authText }}</div>
      </div>
    </div>
  </div>
{% endif %}

<style>
/* Global Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
}

/* Khmer SMM Signin Section - Rectangle Size Only */
.khmer-signin-section {
  background:
    linear-gradient(135deg, #e3f2fd 0%, #f0f4ff 15%, #fef7ff 30%, #fff3e0 45%, #f3e5f5 60%, #e8f5e8 75%, #e1f5fe 90%, #f3e5f5 100%),
    linear-gradient(45deg, rgba(33, 150, 243, 0.1) 0%, transparent 25%, rgba(156, 39, 176, 0.1) 50%, transparent 75%, rgba(255, 193, 7, 0.1) 100%);
  padding: 60px 0;
  position: relative;
  overflow: hidden;
}

.khmer-signin-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(ellipse at 10% 10%, rgba(33, 150, 243, 0.15) 0%, transparent 40%),
    radial-gradient(ellipse at 90% 90%, rgba(156, 39, 176, 0.15) 0%, transparent 40%),
    radial-gradient(ellipse at 30% 70%, rgba(255, 193, 7, 0.12) 0%, transparent 40%),
    radial-gradient(ellipse at 70% 30%, rgba(76, 175, 80, 0.12) 0%, transparent 40%),
    radial-gradient(ellipse at 50% 50%, rgba(244, 67, 54, 0.08) 0%, transparent 40%);
  pointer-events: none;
}

.khmer-signin-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    conic-gradient(from 0deg at 20% 20%, rgba(33, 150, 243, 0.05) 0deg, transparent 60deg),
    conic-gradient(from 180deg at 80% 80%, rgba(156, 39, 176, 0.05) 0deg, transparent 60deg);
  pointer-events: none;
}

.khmer-subtitle {
  font-size: 18px;
  color: #333;
  margin-bottom: 5px;
  font-weight: 400;
  letter-spacing: 0.3px;
}

.khmer-title {
  font-size: 56px;
  font-weight: 900;
  line-height: 1.1;
  margin-bottom: 10px;
}

.blue-text {
  color: #2196f3;
  display: inline;
  font-size: 56px;
  font-weight: 900;
  line-height: 1.1;
}

.black-text {
  color: #000;
  display: inline;
  font-size: 56px;
  font-weight: 900;
  line-height: 1.1;
}

.khmer-description {
  font-size: 16px;
  color: #666;
  margin-bottom: 15px;
  line-height: 1.5;
}

.khmer-form-box {
  background: transparent;
  padding: 0;
  border-radius: 0;
  box-shadow: none;
  max-width: 400px;
}

.khmer-input-group {
  position: relative;
  margin-bottom: 15px;
}

.input-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  z-index: 2;
}

.khmer-input {
  width: 100%;
  height: 45px;
  padding: 0 45px 0 50px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.3s;
  background: #f8f9fa;
}

.khmer-input:focus {
  border-color: #2196f3;
  background: white;
}

.khmer-input::placeholder {
  color: #999;
  font-size: 14px;
}

.password-toggle {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  font-size: 16px;
  color: #999;
}

.khmer-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  font-size: 14px;
}

.khmer-checkbox {
  display: flex;
  align-items: center;
  color: #666;
  cursor: pointer;
}

.khmer-checkbox input {
  margin-right: 8px;
}

.khmer-forgot {
  color: #2196f3;
  text-decoration: none;
}

.khmer-forgot:hover {
  text-decoration: underline;
}

.khmer-buttons-row {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.khmer-signin-btn {
  flex: 1;
  height: 48px;
  background: linear-gradient(135deg, #1e88e5 0%, #2196f3 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
  text-transform: none;
  letter-spacing: 0.5px;
}

.khmer-signin-btn:hover {
  background: linear-gradient(135deg, #1976d2 0%, #1e88e5 100%);
  box-shadow: 0 6px 16px rgba(33, 150, 243, 0.4);
  transform: translateY(-1px);
}

.khmer-google-inline {
  flex: 1;
}

.khmer-google {
  text-align: center;
  margin: 15px 0;
}

.khmer-google-inline .g_id_signin {
  width: 100% !important;
  height: 45px !important;
}

.khmer-google-inline .g_id_signin > div {
  width: 100% !important;
  height: 45px !important;
  border-radius: 5px !important;
  border: 1px solid #ddd !important;
  background: white !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.khmer-signup {
  text-align: center;
  font-size: 14px;
  color: #666;
}

.khmer-signup a {
  color: #2196f3;
  text-decoration: none;
}

.khmer-signup a:hover {
  text-decoration: underline;
}

.khmer-illustration {
  height: 100%;
  min-height: 500px;
  overflow: hidden;
  position: relative;
}

.illustration-image {
  width: 100%;
  height: 100%;
  min-height: 500px;
  object-fit: cover;
  object-position: center;
}

.alert {
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 14px;
}

.alert-danger {
  background: #ffebee;
  color: #c62828;
  border: 1px solid #ffcdd2;
}

.alert-success {
  background: #e8f5e8;
  color: #2e7d32;
  border: 1px solid #c8e6c9;
}

/* Statistics Section */
.khmer-stats-section {
  background: #f8f9fa;
  padding: 60px 0;
  margin-top: 0;
}

.khmer-stat-card {
  background: white;
  padding: 30px 20px;
  border-radius: 10px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0,0,0,0.08);
  margin-bottom: 30px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.khmer-stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.stat-icon {
  margin-bottom: 20px;
}

.stat-icon img {
  width: 60px;
  height: 60px;
  filter: brightness(0) saturate(100%) invert(27%) sepia(96%) saturate(1458%) hue-rotate(201deg) brightness(97%) contrast(101%);
}

/* Special styling for Cambodia rank icon - show original colors */
.khmer-stat-card:nth-child(4) .stat-icon img {
  filter: none;
}

.stat-number {
  font-size: 36px;
  font-weight: 900;
  color: #2196f3;
  margin: 0 0 10px 0;
  line-height: 1;
}

.stat-label {
  font-size: 16px;
  color: #666;
  margin: 0;
  font-weight: 500;
}

@media (max-width: 768px) {
  .khmer-title {
    font-size: 32px;
  }

  .khmer-form-box {
    margin-top: 30px;
  }

  .khmer-stats-section {
    padding: 40px 0;
  }

  .stat-number {
    font-size: 28px;
  }

  .stat-icon img {
    width: 50px;
    height: 50px;
  }
}

/* Services Section */
.khmer-services-section {
  background: white;
  padding: 80px 0;
}

.services-header {
  text-align: center;
  margin-bottom: 60px;
}

.services-subtitle {
  color: #2196f3;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.services-title {
  font-size: 42px;
  font-weight: 900;
  color: #333;
  margin-bottom: 20px;
  line-height: 1.2;
}

.services-title .blue-text {
  color: #2196f3;
  text-decoration: underline;
  text-decoration-color: #2196f3;
  text-decoration-style: wavy;
  text-underline-offset: 4px;
  text-decoration-thickness: 2px;
}

.services-description {
  font-size: 16px;
  color: #666;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
}

.social-icons-container {
  margin-bottom: 60px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.social-icons-row {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.social-icon-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 18px;
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
  justify-content: flex-start;
  flex: 0 0 auto;
}

.social-icon-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  border-color: #2196f3;
}

.social-icon-button.active {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  border-color: #2196f3;
  background: #f8f9ff;
}

.social-icon-button i {
  font-size: 20px;
  width: 24px;
  text-align: center;
}

.social-icon-button span {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

/* Social Media Colors for Icons */
.social-icon-button[data-platform="facebook"] i { color: #1877f2; }
.social-icon-button[data-platform="instagram"] i { color: #e4405f; }
.social-icon-button[data-platform="youtube"] i { color: #ff0000; }
.social-icon-button[data-platform="tiktok"] i { color: #000000; }
.social-icon-button[data-platform="linkedin"] i { color: #0077b5; }
.social-icon-button[data-platform="telegram"] i { color: #0088cc; }
.social-icon-button[data-platform="discord"] i { color: #5865f2; }
.social-icon-button[data-platform="twitter"] i { color: #1da1f2; }
.social-icon-button[data-platform="pinterest"] i { color: #bd081c; }
.social-icon-button[data-platform="twitch"] i { color: #9146ff; }
.social-icon-button[data-platform="soundcloud"] i { color: #ff5500; }
.social-icon-button[data-platform="webtraffic"] i { color: #2196f3; }

/* Social Media Colors */
.facebook { background: #1877f2; }
.instagram { background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%); }
.youtube { background: #ff0000; }
.tiktok { background: #000000; }
.linkedin { background: #0077b5; }
.telegram { background: #0088cc; }
.discord { background: #5865f2; }
.twitter { background: #1da1f2; }
.pinterest { background: #bd081c; }
.twitch { background: #9146ff; }
.soundcloud { background: #ff5500; }
.webtraffic { background: #2196f3; }

/* Transform Section Styles - Full Width */
.transform-section-fullwidth {
  background: linear-gradient(135deg, #e3f2fd 0%, #f8f9fa 100%);
  padding: 80px 0;
  position: relative;
  overflow: hidden;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  margin-right: calc(-50vw + 50%);
}

.transform-section-fullwidth::before {
  content: '';
  position: absolute;
  top: -30%;
  right: -20%;
  width: 60%;
  height: 120%;
  background: radial-gradient(circle, rgba(33, 150, 243, 0.08) 0%, transparent 70%);
  border-radius: 50%;
}

.transform-section-fullwidth::after {
  content: '';
  position: absolute;
  bottom: -40%;
  left: -20%;
  width: 50%;
  height: 100%;
  background: radial-gradient(circle, rgba(33, 150, 243, 0.05) 0%, transparent 70%);
  border-radius: 50%;
}

.transform-title {
  font-size: 42px;
  font-weight: 900;
  color: #333;
  margin-bottom: 25px;
  line-height: 1.2;
}

.highlight-text {
  color: #2196f3;
  position: relative;
}

.transform-description {
  font-size: 18px;
  color: #666;
  line-height: 1.7;
  margin-bottom: 35px;
  max-width: 90%;
}

.transform-btn {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  color: white;
  border: none;
  padding: 18px 35px;
  font-size: 18px;
  font-weight: 600;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
  text-transform: none;
}

.transform-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(33, 150, 243, 0.4);
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
}

.transform-image {
  text-align: center;
  position: relative;
  z-index: 2;
}

.transform-image img {
  max-width: 100%;
  height: auto;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* About Section */
.about-section {
  padding: 80px 0;
  background: #f4ede4;
  position: relative;
  min-height: 600px;
  display: flex;
  align-items: center;
}

.about-container {
  background: #f4ede4;
  border-radius: 0;
  padding: 80px 0;
  margin: 0;
  width: 100vw;
  position: relative;
  overflow: hidden;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
}

.about-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #007bff 0%, #0056b3 50%, #007bff 100%);
  opacity: 0.8;
}

.about-container::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #007bff 0%, #0056b3 50%, #007bff 100%);
  opacity: 0.8;
}

.about-subtitle {
  color: #007bff;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  text-transform: uppercase;
  letter-spacing: 1.5px;
  position: relative;
  display: inline-block;
}

.about-subtitle::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 50px;
  height: 3px;
  background: linear-gradient(90deg, #007bff, #0056b3);
  border-radius: 2px;
}

.about-title {
  font-size: 48px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 30px;
  line-height: 1.2;
  position: relative;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.about-title::before {
  content: '';
  position: absolute;
  left: -25px;
  top: 50%;
  transform: translateY(-50%);
  width: 5px;
  height: 70px;
  background: linear-gradient(180deg, #007bff 0%, #0056b3 50%, #007bff 100%);
  border-radius: 3px;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.about-description {
  font-size: 17px;
  color: #5a6c7d;
  line-height: 1.8;
  margin-bottom: 25px;
  text-align: justify;
  position: relative;
  padding-left: 20px;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);
  font-weight: 400;
}

.about-description:first-of-type {
  font-weight: 500;
  color: #4a5568;
}

.about-description::before {
  content: '"';
  position: absolute;
  left: 0;
  top: -5px;
  font-size: 40px;
  color: #007bff;
  opacity: 0.3;
  font-family: serif;
}

.about-btn {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 50%, #007bff 100%);
  color: white;
  padding: 18px 45px;
  border: 2px solid transparent;
  border-radius: 50px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 1.2px;
  margin-top: 20px;
  box-shadow:
    0 8px 25px rgba(0, 123, 255, 0.25),
    0 3px 10px rgba(0, 123, 255, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.about-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.about-btn:hover::before {
  left: 100%;
}

.about-btn:hover {
  background: linear-gradient(135deg, #0056b3 0%, #004085 50%, #0056b3 100%);
  transform: translateY(-4px) scale(1.02);
  box-shadow:
    0 15px 40px rgba(0, 123, 255, 0.4),
    0 5px 15px rgba(0, 123, 255, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.2);
}

.about-btn:active {
  transform: translateY(-2px) scale(1.01);
  box-shadow:
    0 8px 20px rgba(0, 123, 255, 0.3),
    0 2px 8px rgba(0, 123, 255, 0.2);
}

.about-image {
  text-align: right;
  position: relative;
  padding-right: 40px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.about-image::before {
  content: '';
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  background: linear-gradient(45deg, #007bff, #0056b3, #007bff);
  border-radius: 25px;
  z-index: -1;
  opacity: 0.1;
}

.about-image img {
  max-width: 90%;
  height: auto;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
}

.about-image img:hover {
  transform: scale(1.02);
}

/* Why Choose Section */
.why-choose-section {
  padding: 80px 0;
  background: #ffffff;
  position: relative;
}

.why-choose-container {
  background: #ffffff;
  padding: 80px 0;
  margin: 0;
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
}

.why-choose-subtitle {
  color: #007bff;
  font-size: 16px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 15px;
}

.why-choose-title {
  font-size: 48px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 30px;
  line-height: 1.2;
}

.why-choose-description {
  font-size: 18px;
  color: #666;
  line-height: 1.7;
  max-width: 900px;
  margin: 0 auto 60px;
}

.why-choose-card {
  background: #ffffff;
  border-radius: 15px;
  padding: 40px 30px;
  text-align: center;
  height: 100%;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.why-choose-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.why-choose-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border-radius: 50%;
  padding: 20px;
}

.why-choose-icon img {
  width: 40px;
  height: 40px;
  filter: brightness(0) invert(1);
}

.why-choose-card-title {
  font-size: 22px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 15px;
  line-height: 1.3;
}

.why-choose-card-text {
  font-size: 16px;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* Testimonials Section */
.testimonials-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
}

.testimonials-container {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 80px 0;
  margin: 0;
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
}

.testimonials-image {
  text-align: center;
  position: relative;
  padding: 20px;
}

.testimonials-image img {
  max-width: 100%;
  height: auto;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.testimonials-content {
  padding: 40px;
}

.testimonials-subtitle {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 20px;
  line-height: 1.3;
}

.testimonials-description {
  font-size: 16px;
  color: #666;
  line-height: 1.7;
  margin-bottom: 40px;
}

.testimonial-slider {
  position: relative;
  min-height: 300px;
  overflow: hidden;
}

.testimonial-track {
  display: flex;
  transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  width: 300%;
}

.testimonial-item {
  width: 33.333%;
  flex-shrink: 0;
  opacity: 1;
  transform: translateX(0);
}

.testimonial-quote {
  position: relative;
  margin-bottom: 30px;
}

.quote-icon {
  font-size: 40px;
  color: #007bff;
  margin-bottom: 20px;
}

.testimonial-text {
  font-size: 18px;
  color: #2c3e50;
  line-height: 1.7;
  font-style: italic;
  margin: 0;
  padding-left: 20px;
  border-left: 4px solid #007bff;
}

.testimonial-rating {
  margin-bottom: 20px;
}

.testimonial-rating i {
  color: #ffc107;
  font-size: 18px;
  margin-right: 5px;
}

.testimonial-author h6 {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 5px;
}

.testimonial-author span {
  font-size: 14px;
  color: #666;
}

.testimonial-navigation {
  display: flex;
  gap: 15px;
  margin-top: 30px;
}

.testimonial-nav-btn {
  width: 50px;
  height: 50px;
  border: none;
  border-radius: 50%;
  background: #007bff;
  color: white;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.testimonial-nav-btn:hover {
  background: #0056b3;
  transform: scale(1.1);
}

.testimonial-nav-btn:active {
  transform: scale(0.95);
}

/* Who Can Benefit Section */
.who-benefit-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.who-benefit-container {
  background: #f8f9fa;
  padding: 80px 0;
  margin: 0;
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
}

.who-benefit-title {
  font-size: 42px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 20px;
  line-height: 1.2;
}

.who-benefit-description {
  font-size: 20px;
  color: #555;
  line-height: 1.8;
  max-width: 900px;
  margin: 0 auto;
  font-weight: 500;
}

/* Masonry Layout - True Pinterest Style */
.benefit-masonry-container {
  columns: 2;
  column-gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.benefit-card {
  break-inside: avoid;
  margin-bottom: 20px;
  width: 100%;
  display: inline-block;
}

/* Different heights for masonry effect */
.benefit-card:nth-child(1) {
  height: 320px;
}

.benefit-card:nth-child(2) {
  height: 280px;
}

.benefit-card:nth-child(3) {
  height: 240px;
}

.benefit-card:nth-child(4) {
  height: 260px;
}

.benefit-card:nth-child(5) {
  height: 300px;
}

.benefit-card:nth-child(6) {
  height: 270px;
}

.benefit-card {
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
  margin-bottom: 0;
}

.benefit-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

/* Card colors matching reference exactly */
.benefit-card.blue-card {
  background: linear-gradient(135deg, #1E88E5 0%, #1565C0 100%);
  color: white;
}

.benefit-card.purple-card {
  background: linear-gradient(135deg, #8E24AA 0%, #6A1B9A 100%);
  color: white;
}

.benefit-card.light-card {
  background: linear-gradient(135deg, #E8F4FD 0%, #D1E7DD 100%);
  color: #2E7D32;
  border: 1px solid #E3F2FD;
}

.benefit-card.orange-card {
  background: linear-gradient(135deg, #FB8C00 0%, #EF6C00 100%);
  color: white;
}

.benefit-content {
  flex: 1;
  padding-right: 20px;
  z-index: 2;
  position: relative;
}

.benefit-card-title {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 16px;
  line-height: 1.3;
  letter-spacing: -0.5px;
}

.benefit-card-text {
  font-size: 16px;
  line-height: 1.7;
  margin-bottom: 0;
  opacity: 0.95;
  font-weight: 400;
}

.benefit-image {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  z-index: 2;
  align-self: center;
}

.benefit-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* FAQ Section - Exact Reference Style */
.faq-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.faq-container {
  background: #f8f9fa;
  padding: 80px 0;
  margin: 0;
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
}

.faq-title {
  font-size: 48px;
  font-weight: 700;
  color: #333;
  margin-bottom: 20px;
  line-height: 1.2;
}

.faq-description {
  font-size: 16px;
  color: #666;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto 50px auto;
}

.faq-item {
  background: white;
  border-radius: 8px;
  margin-bottom: 15px;
  border: 1px solid #e8e8e8;
  overflow: visible;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 1;
}

.faq-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.faq-item.active {
  z-index: 10;
}

/* FAQ Grid Layout */
.faq-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  align-items: start;
}

.faq-column {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.faq-column-left {
  grid-column: 1;
}

.faq-column-right {
  grid-column: 2;
}

/* Mobile responsive */
@media (max-width: 991px) {
  .faq-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .faq-column-left,
  .faq-column-right {
    grid-column: 1;
  }
}

/* Legacy Bootstrap column support */
.faq-section .col-lg-6 {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.faq-section .faq-item {
  width: 100%;
  flex-shrink: 0;
  position: relative;
}

/* Footer Styles */
.footer-section {
  background: linear-gradient(135deg, #1a2332 0%, #2c3e50 100%);
  color: #ffffff;
  padding: 60px 0 25px 0;
  margin-top: 80px;
  margin-bottom: 0;
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
}

.footer-brand {
  margin-bottom: 30px;
}

.footer-logo {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.logo-icon {
  font-size: 28px;
  margin-right: 10px;
}

.logo-text {
  font-size: 24px;
  font-weight: bold;
  color: #4a9eff;
  letter-spacing: 1px;
}

.footer-description {
  color: #b8c5d1;
  line-height: 1.6;
  margin-bottom: 25px;
  font-size: 14px;
}

.social-media-icons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.social-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 16px;
}

.social-icon.facebook {
  background-color: #3b5998;
  color: white;
}

.social-icon.youtube {
  background-color: #ff0000;
  color: white;
}

.social-icon.twitter {
  background-color: #1da1f2;
  color: white;
}

.social-icon.telegram {
  background-color: #0088cc;
  color: white;
}

.social-icon.instagram {
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  color: white;
}

.social-icon.skype {
  background-color: #00aff0;
  color: white;
}

.social-icon:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.footer-title {
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  position: relative;
}

.footer-title::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -8px;
  width: 30px;
  height: 2px;
  background-color: #4a9eff;
}

.footer-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-menu li {
  margin-bottom: 12px;
}

.footer-menu a {
  color: #b8c5d1;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer-menu a:hover {
  color: #4a9eff;
}

.footer-contact {
  margin-top: 10px;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
}

.contact-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  flex-shrink: 0;
}

.contact-icon.location {
  background-color: #4a9eff;
  color: white;
}

.contact-icon.email {
  background-color: #4a9eff;
  color: white;
}

.contact-info p {
  margin: 0;
  color: #b8c5d1;
  font-size: 14px;
  line-height: 1.5;
}

.contact-info .telegram-link {
  color: #4a9eff;
  text-decoration: none;
  font-weight: 500;
}

.contact-info .telegram-link:hover {
  color: #ffffff;
}

.footer-bottom {
  border-top: 1px solid #34495e;
  margin-top: 40px;
  margin-bottom: 0;
  padding: 25px 0;
}

.footer-copyright {
  text-align: center;
}

.footer-copyright p {
  margin: 0 !important;
  padding: 0 !important;
  color: #b8c5d1;
  font-size: 14px;
  line-height: 1.4;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .footer-section {
    padding: 40px 0 0;
  }

  .social-media-icons {
    justify-content: center;
  }

  .footer-title {
    text-align: center;
  }

  .footer-title::after {
    left: 50%;
    transform: translateX(-50%);
  }

  .footer-menu {
    text-align: center;
  }

  .contact-item {
    justify-content: center;
    text-align: left;
  }
}

.faq-question {
  padding: 20px 24px;
  background: white;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  outline: none;
  user-select: none;
}

.faq-question:hover {
  background: #f8f9fa;
}

.faq-question.active {
  background: #f8f9fa;
}

.faq-icon {
  font-size: 14px;
  color: #666;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  margin-left: 16px;
}

.faq-question.active .faq-icon {
  transform: rotate(180deg);
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: white;
  border-top: 1px solid #f0f0f0;
}

.faq-answer.active {
  max-height: 300px;
}

.faq-content {
  padding: 0 24px 20px 24px;
  font-size: 14px;
  line-height: 1.6;
  color: #666;
}

.telegram-link, .telegram-contact {
  color: #0088cc;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.telegram-link:hover, .telegram-contact:hover {
  color: #006699;
  text-decoration: underline;
}

.faq-contact {
  font-size: 16px;
  color: #666;
  margin-bottom: 10px;
}

.faq-branding {
  font-size: 14px;
  color: #666;
  font-weight: 500;
  margin: 0;
}

/* About Modal Styles */
.modal {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  z-index: 9999 !important;
  display: none !important;
  width: 100% !important;
  height: 100% !important;
  overflow-x: hidden;
  overflow-y: auto;
  outline: 0;
  background-color: rgba(0,0,0,0.5);
}

.modal.show {
  display: block !important;
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: 0.5rem;
  pointer-events: none;
}

.modal-dialog-centered {
  display: flex;
  align-items: center;
  min-height: calc(100% - 1rem);
}

.modal-xl {
  max-width: 1200px;
}

.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem;
  outline: 0;
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.5);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1rem;
  border-bottom: 1px solid #dee2e6;
  border-top-left-radius: calc(0.3rem - 1px);
  border-top-right-radius: calc(0.3rem - 1px);
}

.modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: 1rem;
  max-height: 70vh;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-end;
  padding: 0.75rem;
  border-top: 1px solid #dee2e6;
  border-bottom-right-radius: calc(0.3rem - 1px);
  border-bottom-left-radius: calc(0.3rem - 1px);
}

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1050;
  width: 100vw;
  height: 100vh;
  background-color: #000;
  opacity: 0.5;
}

.btn-close {
  box-sizing: content-box;
  width: 1em;
  height: 1em;
  padding: 0.25em 0.25em;
  color: #000;
  background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='m.235.867 4.596 4.596 4.596-4.596a.5.5 0 0 1 .708.708L5.939 6.171l4.596 4.596a.5.5 0 0 1-.708.708L5.231 6.879.635 11.475a.5.5 0 0 1-.708-.708L4.523 6.171.027 1.575a.5.5 0 0 1 .708-.708z'/%3e%3c/svg%3e") center/1em auto no-repeat;
  border: 0;
  border-radius: 0.25rem;
  opacity: 0.5;
  cursor: pointer;
}

.btn-close:hover {
  opacity: 0.75;
}

.modal-hero-image {
  max-width: 400px;
  width: 100%;
  height: auto;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.modal-section-title {
  font-size: 36px;
  font-weight: 700;
  color: #333;
  margin-bottom: 10px;
}

.modal-subtitle {
  font-size: 18px;
  color: #007bff;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.modal-heading {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin-bottom: 20px;
  border-bottom: 3px solid #007bff;
  padding-bottom: 10px;
}

.modal-text {
  font-size: 16px;
  color: #666;
  line-height: 1.8;
  margin-bottom: 20px;
  text-align: justify;
}

.feature-list {
  padding: 0;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 25px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  font-size: 24px;
  color: #007bff;
  margin-right: 15px;
  margin-top: 5px;
  min-width: 30px;
}

.feature-item h5 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.feature-item p {
  font-size: 14px;
  color: #666;
  margin: 0;
  line-height: 1.6;
}

.service-card {
  background: white;
  padding: 30px 20px;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  height: 100%;
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.service-card-icon {
  font-size: 48px;
  margin-bottom: 20px;
  color: #007bff;
}

.service-card h5 {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.service-card p {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
}

.stat-card {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  padding: 40px 20px;
  border-radius: 15px;
  text-align: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 123, 255, 0.3);
}

.stat-number {
  font-size: 48px;
  font-weight: 900;
  margin-bottom: 10px;
}

.stat-label {
  font-size: 16px;
  font-weight: 500;
  opacity: 0.9;
  margin: 0;
}

.tech-heading {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.tech-list {
  list-style: none;
  padding: 0;
}

.tech-list li {
  font-size: 15px;
  color: #666;
  margin-bottom: 10px;
  padding-left: 25px;
  position: relative;
}

.tech-list li:before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #007bff;
  font-weight: bold;
}

.cta-buttons .btn {
  padding: 15px 30px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  text-decoration: none;
  transition: all 0.3s ease;
}

.cta-buttons .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* How It Works Section Styles */
.how-it-works-section {
  padding: 80px 0;
  background: #ffffff;
}

.section-subtitle {
  color: #2196f3;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.section-title {
  font-size: 48px;
  font-weight: 900;
  color: #333;
  margin-bottom: 5px;
  line-height: 1.2;
}

.section-subtitle-main {
  font-size: 32px;
  font-weight: 700;
  color: #333;
  margin-bottom: 25px;
  line-height: 1.3;
}

.section-description {
  font-size: 16px;
  color: #666;
  line-height: 1.7;
  max-width: 800px;
  margin: 0 auto 60px;
}

.step-card {
  text-align: center;
  padding: 40px 20px;
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 15px;
  position: relative;
}

.step-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.step-number {
  background: #333;
  color: white;
  width: 80px;
  height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto 25px;
  position: relative;
  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
}

.step-num {
  font-size: 24px;
  font-weight: 900;
  line-height: 1;
}

.step-label {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.step-title {
  font-size: 20px;
  font-weight: 700;
  color: #333;
  margin-bottom: 15px;
}

.step-description {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
}

.featured-service {
  background: #f8f9fa;
  padding: 40px;
  border-radius: 15px;
  margin-top: 40px;
  transition: all 0.5s ease;
  min-height: 200px;
  max-height: 300px;
  overflow: hidden;
}

.featured-service.fade-out {
  opacity: 0;
  transform: translateY(20px);
}

.featured-service.fade-in {
  opacity: 1;
  transform: translateY(0);
}

.featured-title {
  font-size: 32px;
  font-weight: 900;
  color: #333;
  margin-bottom: 20px;
}

.featured-description {
  font-size: 16px;
  color: #666;
  line-height: 1.6;
  max-height: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
}

.featured-logo {
  text-align: center;
}

.featured-logo i {
  font-size: 120px;
  color: #1877f2;
}

@media (max-width: 768px) {
  .about-section {
    padding: 60px 0;
  }

  .about-container {
    padding: 60px 0;
    margin: 0;
    width: 100vw;
    border-radius: 0;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
  }

  .about-title {
    font-size: 32px;
    margin-bottom: 20px;
  }

  .about-title::before {
    display: none;
  }

  .about-description {
    font-size: 15px;
    margin-bottom: 20px;
    padding-left: 0;
    text-align: left;
  }

  .about-description::before {
    display: none;
  }

  .about-btn {
    padding: 15px 30px;
    font-size: 14px;
    width: 100%;
    margin-top: 30px;
  }

  .about-image::before {
    display: none;
  }

  /* Why Choose Mobile Styles */
  .why-choose-title {
    font-size: 32px;
  }

  .why-choose-description {
    font-size: 16px;
    margin-bottom: 40px;
  }

  .why-choose-card {
    padding: 30px 20px;
    margin-bottom: 20px;
  }

  .why-choose-icon {
    width: 70px;
    height: 70px;
    margin-bottom: 20px;
  }

  .why-choose-icon img {
    width: 35px;
    height: 35px;
  }

  .why-choose-card-title {
    font-size: 20px;
  }

  .why-choose-card-text {
    font-size: 15px;
  }

  /* Testimonials Mobile Styles */
  .testimonials-content {
    padding: 20px;
    margin-top: 30px;
  }

  .testimonials-subtitle {
    font-size: 24px;
    text-align: center;
  }

  .testimonials-description {
    font-size: 15px;
    text-align: center;
    margin-bottom: 30px;
  }

  .testimonial-text {
    font-size: 16px;
    padding-left: 15px;
  }

  .quote-icon {
    font-size: 30px;
    text-align: center;
    display: block;
    margin-bottom: 15px;
  }

  .testimonial-author {
    text-align: center;
  }

  .testimonial-navigation {
    justify-content: center;
    margin-top: 25px;
  }

  .testimonial-nav-btn {
    width: 45px;
    height: 45px;
    font-size: 16px;
  }

  /* Who Can Benefit Mobile Styles */
  .who-benefit-title {
    font-size: 32px;
    text-align: center;
    margin-bottom: 15px;
  }

  .who-benefit-description {
    font-size: 15px;
    text-align: center;
    padding: 0 15px;
    margin-bottom: 40px;
  }

  .benefit-masonry-container {
    columns: 1;
    gap: 15px;
    padding: 0 15px;
  }

  .benefit-card {
    padding: 20px;
    margin-bottom: 15px;
    flex-direction: column;
    text-align: center;
  }

  .benefit-content {
    padding-right: 0;
    margin-bottom: 15px;
  }

  .benefit-card-title {
    font-size: 20px;
  }

  .benefit-card-text {
    font-size: 15px;
  }

  .benefit-image {
    align-self: center;
    width: 60px;
    height: 60px;
  }

  /* FAQ Mobile Styles */
  .faq-title {
    font-size: 36px;
    text-align: center;
    padding: 0 15px;
    margin-bottom: 20px;
  }

  .faq-description {
    font-size: 14px;
    text-align: center;
    padding: 0 15px;
    margin-bottom: 30px;
  }

  .faq-question {
    padding: 16px 20px;
    font-size: 14px;
  }

  .faq-content {
    padding: 0 20px 16px 20px;
    font-size: 13px;
  }

  .faq-contact {
    font-size: 14px;
    text-align: center;
    padding: 0 15px;
  }

  .faq-branding {
    font-size: 12px;
    text-align: center;
    padding: 0 15px;
  }

  .benefit-content {
    margin-bottom: 10px;
  }

  .benefit-card-title {
    font-size: 16px;
    margin-bottom: 8px;
  }

  .benefit-card-text {
    font-size: 12px;
    line-height: 1.4;
  }

  .benefit-image {
    bottom: 10px;
    right: 10px;
  }

  .benefit-image img {
    max-width: 50px;
  }

  /* Modal Mobile Styles */
  .modal-hero-image {
    max-width: 300px;
  }

  .modal-section-title {
    font-size: 28px;
  }

  .modal-subtitle {
    font-size: 16px;
  }

  .modal-heading {
    font-size: 20px;
  }

  .modal-text {
    font-size: 14px;
  }

  .feature-item {
    padding: 15px;
    margin-bottom: 20px;
  }

  .service-card {
    padding: 20px 15px;
    margin-bottom: 20px;
  }

  .service-card-icon {
    font-size: 36px;
  }

  .stat-number {
    font-size: 36px;
  }

  .cta-buttons .btn {
    padding: 12px 25px;
    font-size: 14px;
    margin-bottom: 10px;
    display: block;
    width: 100%;
  }

  .services-title {
    font-size: 28px;
  }

  .social-icons-container {
    width: 85%;
  }

  .social-icons-row {
    gap: 10px;
    justify-content: center;
  }

  .social-icon-button {
    padding: 10px 15px;
    min-width: 110px;
    justify-content: center;
    flex: 0 0 calc(50% - 5px);
  }

  .social-icon-button i {
    font-size: 18px;
  }

  .social-icon-button span {
    font-size: 12px;
  }

  .featured-logo i {
    font-size: 80px;
  }

  .featured-service {
    padding: 20px;
    min-height: 150px;
    max-height: 200px;
  }

  .featured-title {
    font-size: 24px;
  }

  .featured-description {
    font-size: 14px;
    max-height: 80px;
    -webkit-line-clamp: 3;
  }

  .transform-section-fullwidth {
    padding: 60px 20px;
  }

  .transform-title {
    font-size: 28px;
    text-align: center;
    margin-bottom: 20px;
  }

  .transform-description {
    font-size: 16px;
    text-align: center;
    max-width: 100%;
    margin-bottom: 25px;
  }

  .transform-btn {
    width: 100%;
    padding: 15px 25px;
    font-size: 16px;
    margin-bottom: 30px;
  }

  .transform-image {
    margin-top: 20px;
  }

  .how-it-works-section {
    padding: 60px 0;
  }

  .section-title {
    font-size: 32px;
  }

  .section-subtitle-main {
    font-size: 24px;
  }

  .section-description {
    font-size: 14px;
    margin-bottom: 40px;
  }

  .step-card {
    padding: 30px 15px;
    margin-bottom: 20px;
  }

  .step-number {
    width: 70px;
    height: 70px;
    margin-bottom: 20px;
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
  }

  .step-num {
    font-size: 20px;
  }

  .step-title {
    font-size: 18px;
  }

  .step-description {
    font-size: 13px;
  }
}
</style>

<script>
// Social Media Platform Data
const platformData = {
  facebook: {
    title: "Facebook SMM Panel",
    description: "Boost your Facebook page engagement and reach with our expert marketing strategies. � We'll make your page so hot, even your ex will slide into your DMs asking 'how you doing?' �💦",
    icon: "fab fa-facebook-f",
    color: "#1877f2"
  },
  instagram: {
    title: "Instagram SMM Panel",
    description: "Grow your Instagram followers, likes, and engagement organically and effectively. 📸 Your thirst traps will get the attention they deserve... we'll make sure everyone's sliding up on your stories �🍑",
    icon: "fab fa-instagram",
    color: "#e4405f"
  },
  youtube: {
    title: "YouTube SMM Panel",
    description: "Increase YouTube subscribers, views, and engagement for channel growth. 🎬 Your content will be so addictive, viewers won't be able to pull out... of the rabbit hole 😈💯",
    icon: "fab fa-youtube",
    color: "#ff0000"
  },
  tiktok: {
    title: "TikTok SMM Panel",
    description: "Grow your TikTok following with our targeted SMM services. 🎵 Your moves will be so fire, everyone will want a piece of that action... we'll make sure you go viral in all the right ways �🍆",
    icon: "fab fa-tiktok",
    color: "#000000"
  },
  linkedin: {
    title: "LinkedIn SMM Panel",
    description: "Expand your professional network on LinkedIn with our business-focused SMM services. 💼 We'll help you get on top in the business world... your professional game will be so strong, headhunters will be begging for you 😈�",
    icon: "fab fa-linkedin-in",
    color: "#0077b5"
  },
  telegram: {
    title: "Telegram SMM Panel",
    description: "Grow your Telegram channels and groups with our specialized services. 📱 Your channel will be so steamy, members won't be able to resist joining... we know how to make things grow big and fast 💦🔥",
    icon: "fab fa-telegram-plane",
    color: "#0088cc"
  },
  discord: {
    title: "Discord SMM Panel",
    description: "Build your Discord server community with our targeted SMM services. 🎮 Your server will be so hot and active, members will be coming back for more... we'll make sure your community is always satisfied 😈🔥",
    icon: "fab fa-discord",
    color: "#5865f2"
  },
  twitter: {
    title: "X-Twitter SMM Panel",
    description: "Enhance your Twitter/X presence with our comprehensive SMM services. 🐦 Your tweets will be so spicy, followers won't be able to resist retweeting... we'll make you trend in all the naughty ways �😏",
    icon: "fab fa-twitter",
    color: "#1da1f2"
  },
  pinterest: {
    title: "Pinterest SMM Panel",
    description: "Boost your Pinterest boards and pins with our creative SMM services. 📌 Your pins will be so tempting, people won't be able to resist saving them... we know how to make things stick 😈✨",
    icon: "fab fa-pinterest-p",
    color: "#bd081c"
  },
  twitch: {
    title: "Twitch SMM Panel",
    description: "Grow your Twitch streaming audience with our gaming-focused SMM services. 🎮 Your stream will be so hot, viewers will be donating just to get your attention... we'll make sure your chat is always wet with excitement �🔥",
    icon: "fab fa-twitch",
    color: "#9146ff"
  },
  soundcloud: {
    title: "SoundCloud SMM Panel",
    description: "Amplify your music on SoundCloud with our audio-focused SMM services. 🎵 Your beats will be so dirty, listeners won't be able to resist hitting repeat... we'll make your tracks climax on the charts 😈🔥",
    icon: "fab fa-soundcloud",
    color: "#ff5500"
  },
  webtraffic: {
    title: "Web Traffic SMM Panel",
    description: "Drive quality traffic to your website with our comprehensive web traffic services. 🌐 Your site will be so irresistible, visitors won't be able to pull out... we'll make sure they keep coming back for more 💦�",
    icon: "fas fa-globe",
    color: "#2196f3"
  }
};

// Add click event listeners to social media icons
document.addEventListener('DOMContentLoaded', function() {
  const socialIcons = document.querySelectorAll('.social-icon-button');

  socialIcons.forEach(icon => {
    icon.addEventListener('click', function() {
      const platform = this.getAttribute('data-platform');
      const data = platformData[platform];

      if (data) {
        const featuredService = document.getElementById('featured-service');

        // Add fade-out effect
        featuredService.classList.add('fade-out');

        // Wait for fade-out to complete, then update content
        setTimeout(() => {
          // Update the featured service section content
          document.getElementById('service-title').textContent = data.title;
          document.getElementById('service-description').textContent = data.description;
          document.getElementById('service-icon').className = data.icon;
          document.getElementById('service-icon').style.color = data.color;

          // Remove fade-out and add fade-in
          featuredService.classList.remove('fade-out');
          featuredService.classList.add('fade-in');

          // Remove fade-in class after animation completes
          setTimeout(() => {
            featuredService.classList.remove('fade-in');
          }, 500);

        }, 250); // Half of the transition duration

        // Add active state to clicked icon
        socialIcons.forEach(item => item.classList.remove('active'));
        this.classList.add('active');

        // Smooth scroll to featured service
        featuredService.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
      }
    });
  });
});

// Testimonial Slider Functionality
let currentTestimonial = 0;
const totalTestimonials = 3;

function changeTestimonial(direction) {
  const track = document.querySelector('.testimonial-track');
  if (!track) return;

  // Calculate next testimonial
  currentTestimonial += direction;

  if (currentTestimonial >= totalTestimonials) {
    currentTestimonial = 0;
  } else if (currentTestimonial < 0) {
    currentTestimonial = totalTestimonials - 1;
  }

  // Calculate the transform value for smooth sliding
  const translateX = -currentTestimonial * (100 / totalTestimonials);

  // Apply smooth sliding animation
  track.style.transform = `translateX(${translateX}%)`;
}

// Auto-advance testimonials every 5 seconds
setInterval(() => {
  changeTestimonial(1);
}, 5000);

// Initialize the slider
document.addEventListener('DOMContentLoaded', function() {
  const track = document.querySelector('.testimonial-track');
  if (track) {
    track.style.transform = 'translateX(0%)';
  }
});

// FAQ Smooth Toggle Function
function toggleFAQ(element) {
  const faqItem = element.parentElement;
  const faqAnswer = faqItem.querySelector('.faq-answer');
  const faqIcon = element.querySelector('.faq-icon');

  // Toggle current FAQ item
  const isActive = faqAnswer.classList.contains('active');

  if (isActive) {
    // Close current item
    faqItem.classList.remove('active');
    faqAnswer.classList.remove('active');
    element.classList.remove('active');
    faqIcon.style.transform = 'rotate(0deg)';
    faqAnswer.style.maxHeight = '0';
  } else {
    // Open current item
    faqItem.classList.add('active');
    faqAnswer.classList.add('active');
    element.classList.add('active');
    faqIcon.style.transform = 'rotate(180deg)';

    // Calculate and set max-height for smooth animation
    const content = faqAnswer.querySelector('.faq-content');
    const contentHeight = content.scrollHeight + 40; // Add padding
    faqAnswer.style.maxHeight = contentHeight + 'px';
  }
}

// Initialize FAQ on page load
document.addEventListener('DOMContentLoaded', function() {
  // Set initial max-heights for all FAQ answers
  const faqAnswers = document.querySelectorAll('.faq-answer');
  faqAnswers.forEach(answer => {
    const content = answer.querySelector('.faq-content');
    const contentHeight = content.scrollHeight;
    answer.setAttribute('data-height', contentHeight);
  });
});

</script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>