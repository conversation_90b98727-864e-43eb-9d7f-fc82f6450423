<div class="container-fluid">
  <div class="row">
    <div class="col-lg-12">
      <ul class="nav nav-pills {% if site['rtl'] %} rtl-nav {% endif %}">
        <li {% if 'all' == status %}class="active"{% endif %}><a href="{{ page['url'] }}">{{ lang('refill.all') }}</a></li>
        <li {% if 'pending' == status %}class="active"{% endif %}><a href="{{ page['url'] }}/pending">{{ lang('refill.status.pending') }}</a></li>
        <li {% if 'inprogress' == status %}class="active"{% endif %}><a href="{{ page['url'] }}/inprogress">{{ lang('refill.status.inprogress') }}</a></li>
        <li {% if 'completed' == status %}class="active"{% endif %}><a href="{{ page['url'] }}/completed">{{ lang('refill.status.completed') }}</a></li>
        <li {% if 'rejected' == status %}class="active"{% endif %}><a href="{{ page['url'] }}/rejected">{{ lang('refill.status.rejected') }}</a></li>
        <li {% if 'error' == status %}class="active"{% endif %}><a href="{{ page['url'] }}/error">{{ lang('refill.status.error') }}</a></li>
        <li class="pull-right search">
          <form action="{{ page['url'] }}" method="get" id="history-search">
            <div class="input-group">
              <input type="text" name="search" class="form-control" value="{{ search }}"
                     placeholder="{{ lang('general.search.placeholder') }}">
              <span class="input-group-btn">
                <button type="submit" class="btn btn-default"><i class="fa fa-search" aria-hidden="true"></i></button>
              </span>
            </div>
          </form>
        </li>
      </ul>
      <div class="well well-float">
        <table class="table {% if site['rtl'] %} rtl-table {% endif %}">
          <thead>
          <tr>
            <th>{{ lang('refill.id') }}</th>
            <th>{{ lang('refill.date') }}</th>
            <th>{{ lang('refill.order_id') }}</th>
            <th class="width-40">{{ lang('refill.link') }}</th>
            <th>{{ lang('refill.service') }}</th>
            <th>{{ lang('refill.status') }}</th>
          </tr>
          </thead>
          <tbody>
          {% for refill in refillList %}
            <tr>
              <td>{{ refill['id'] }}</td>
              <td>{{ refill['date'] }}</td>
              <td>
                <a href="{{ page_url('orders') }}?search={{ refill['order_id'] }}">{{ refill['order_id'] }}</a>
              </td>
              <td class="width-40">{{ refill['link'] }}</td>
              <td>{{ refill['service'] }}</td>
              <td>{{ refill['status'] }}</td>
            </tr>
          {% endfor %}
          </tbody>
        </table>
      </div>

      {% if pagination['count'] > 100 %}
        {% if searchList %}
          {% set params = {} %}
          {% for search in searchList %}
            {% set params = params | merge([search['name'] ~ '=' ~ search['value']]) %}
          {% endfor %}
          {% set params = '?' ~ params|join('&') %}
        {% endif %}
        <ul class="pagination {% if site['rtl'] %} rtl-pagination {% endif %}">
          {% if pagination['current'] != 1 %}
            <li>
            <a href="{{ page['url'] }}/{{ status }}/{{ pagination['last'] }}{{ params }}" aria-label="Previous">
              <span aria-hidden="true">&laquo;</span>
            </a>
            </li>
          {% endif %}

          {% set r, l = 3, 3 %}

          {% if pagination['current'] == 1 %}
            {% set r = 6 %}
          {% endif %}

          {% if pagination['current'] == 2 %}
            {% set r = 5 %}
          {% endif %}

          {% if pagination['current'] >= pagination['pages'] %}
            {% set l = 5 %}
          {% endif %}

          {% for i in 1..ceil(pagination['pages']) %}
            {% if i >= (pagination['current']-l) and i <= (pagination['current']+r) %}
              <li{% if i == pagination['current'] %} class="active"{% endif %}><a
                    href="{{ page['url'] }}/{{ status }}/{{ i }}{{ params }}">{{ i }}</a></li>
            {% endif %}
          {% endfor %}

          {% if pagination['current'] < pagination['pages'] %}
            <li>
              <a href="{{ page['url'] }}/{{ status }}/{{ pagination['next'] }}{{ params }}" aria-label="Next">
                <span aria-hidden="true">&raquo;</span>
              </a>
            </li>
          {% endif %}
        </ul>
      {% endif %}
    </div>
  </div>
</div>
