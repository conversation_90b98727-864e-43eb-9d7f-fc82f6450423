<div class="container">
  <div class="row">
    <div class="col-md-8 col-md-offset-2">
      <div class="well confirm-email">
        <form {% if site['rtl'] %}class="rtl-form"{% endif %} method="post" action="">
          <div class="row">
            <div class="col-xs-12 text-center">
              {% if successResend %}
                <div class="alert alert-dismissible alert-success {% if site['rtl'] %} rtl-alert {% endif %}">
                  <button type="button" class="close" data-dismiss="alert">&times;</button>
                  {{ successResendMessage }}
                </div>
              {% endif %}
              {% if errorResend %}
                <div class="alert alert-dismissible alert-danger {% if site['rtl'] %} rtl-alert {% endif %}">
                  <button type="button" class="close" data-dismiss="alert">&times;</button>
                  {{ errorResendMessage }}
                </div>
              {% endif %}
              <h3>{{ lang('confirmemail.message.title') }}</h3>
            </div>
          </div>
          <div class="row">
            <div class="col-md-12">
              <div class="confirm-email__description text-center">
                  {% if isSelfChangeEmail %}
                    <div>{{ lang('confirmemail.message.sent') }} {{ user['email'] }}</div>
                    <div>{{ lang('confirmemail.message.description') }}</div>
                  {% else %}
                      {{ lang('confirmemail.message.description') }}
                  {% endif %}
              </div>
            </div>
            <div class="confirm-email__action text-center">
              <button class="btn btn-primary" id="resendEmailLink">{{ lang('confirmemail.button.resend') }}</button>
              {% if can_change_email == 1 %}
                <button class="btn btn-link" id="changeEmailLink">{{ lang('confirmemail.button.change') }}</button>
              {% endif %}
            </div>
          </div>

          <input type="hidden" name="_csrf" value="{{ csrftoken }}">
        </form>
      </div>
    </div>
  </div>
</div>

{% if can_change_email == 1 %}
  <!-- Change email -->
  <div class="modal fade" tabindex="-1" role="dialog" id="changeEmailModal" data-backdrop="static">
    <div class="modal-dialog" role="document">
      <form id="changeEmailForm" class="modal-content{% if site['rtl'] %} rtl-form{% endif %}" method="post" action="">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
          <h4 class="modal-title">{{ lang('confirmemail.change_email.title') }}</h4>
        </div>
        <div class="modal-body">
          <div id="changeEmailError" class="error-summary alert alert-dismissible alert-danger hidden{% if site['rtl'] %} rtl-alert {% endif %}"></div>
          <div class="form-group">
            <label for="current-email">{{ lang('confirmemail.change_email.current_email') }}</label>
            <span class="form-control" id="current-email" disabled>{{ current_email }}</span>
          </div>
          <div class="form-group">
            <label for="new-email">{{ lang('confirmemail.change_email.new_email') }}</label>
            <input type="email" class="form-control" id="new-email" name="ChangeConfirmationEmailForm[email]">
          </div>
          <div class="form-group">
            <label for="current-password">{{ lang('confirmemail.change_email.current_password') }}</label>
            <input type="password" class="form-control" id="current-password" name="ChangeConfirmationEmailForm[password]">
          </div>
          <input type="hidden" name="_csrf" value="{{ csrftoken }}">
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">{{ lang('confirmemail.change_email.close') }}</button>
          <button type="submit" class="btn btn-primary" id="changeEmailSubmitBtn">{{ lang('confirmemail.change_email.submit') }}</button>
        </div>
      </form>
    </div>
  </div>
{% endif %}
