{% if site['resetPassword'] %}
  <div class="container">
    <div class="row">
      <div class="col-md-8 col-md-offset-2">
        <div class="well">
          <form {% if site['rtl'] %}class="rtl-form"{% endif %} method="post" action="">
            <h3>{{  lang('resetpassword.setnewpassword.title') }}</h3>
            {% if error %}
              <div class="alert alert-dismissible alert-danger {% if site['rtl'] %} rtl-alert {% endif %}">
                <button type="button" class="close" data-dismiss="alert">&times;</button>
                {{ errorMessage }}
              </div>
            {% endif %}
            {% if success %}
              <div class="alert alert-dismissible alert-success {% if site['rtl'] %} rtl-alert {% endif %}">
                <button type="button" class="close" data-dismiss="alert">&times;</button>
                {{ successText }}
              </div>
            {% endif %}
            <div class="form-group">
              <label for="username" class="control-label">{{ lang('resetpassword.username') }}</label>
              <input type="text" class="form-control" id="username" value="{{ user['username'] }}" readonly>
            </div>
            <div class="form-group">
              <label for="password_new" class="control-label">{{ lang('resetpassword.new_password') }}</label>
              <input type="password" class="form-control" id="password_new" name="ResetPasswordForm[password]">
            </div>
            <div class="form-group">
              <label for="password_confirm" class="control-label">{{ lang('resetpassword.confirm_new_password') }}</label>
              <input type="password" class="form-control" id="password_confirm" name="ResetPasswordForm[password_again]">
            </div>
            <button type="submit" class="btn btn-primary">{{ lang('resetpassword.button.submit') }}</button>
            <input type="hidden" name="_csrf" value="{{csrftoken}}">
          </form>
        </div>
      </div>
    </div>
  </div>
{% endif %}

{% if site['captcha'] %}
  <script src='https://www.google.com/recaptcha/api.js?hl={{ site['iso_lang_code'] }}'></script>
{% endif %}


